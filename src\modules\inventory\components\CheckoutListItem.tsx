import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Minus, Plus, Trash2 } from "lucide-react";

export type CheckoutItemType = "consumable" | "asset";

export interface CheckoutListItemProps {
  name: string;
  imageUrl?: string;
  type: CheckoutItemType;
  quantity: number;
  onIncrement?: () => void;
  onDecrement?: () => void;
  onRemove?: () => void;
  onQuantityChange?: (qty: number) => void;
}

const typeStyles: Record<CheckoutItemType, string> = {
  consumable: "bg-amber-100 text-amber-700",
  asset: "bg-sky-100 text-sky-700",
};

export default function CheckoutListItem({
  name,
  imageUrl,
  type,
  quantity,
  onIncrement,
  onDecrement,
  onRemove,
  onQuantityChange,
}: CheckoutListItemProps) {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value || "0", 10);
    if (onQuantityChange) onQuantityChange(Math.max(0, value));
  };

  return (
    <div className="border rounded-lg p-3">
      <div className="flex items-start gap-3">
        <img
          src={imageUrl || "/placeholder.svg"}
          alt={name}
          className="size-12 rounded-md object-cover bg-gray-100"
        />
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <p className="font-medium text-sm leading-5 truncate pr-6">{name}</p>
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={onRemove}
              className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
              aria-label="Remove item"
            >
              <Trash2 className="size-5" />
            </Button>
          </div>
          <Badge className={`mt-2 ${typeStyles[type]}`}>{type.toUpperCase()}</Badge>
        </div>
      </div>

      <div className="my-3 border-t" />

      <div className="flex items-center gap-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onIncrement}
          aria-label="Increase quantity"
        >
          <Plus className="size-4" />
        </Button>
        <Input
          type="number"
          min={0}
          value={quantity}
          onChange={handleInputChange}
          className="h-9 w-16 text-center"
        />
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onDecrement}
          aria-label="Decrease quantity"
          disabled={quantity <= 0}
        >
          <Minus className="size-4" />
        </Button>
      </div>
    </div>
  );
}

