import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Minus, Plus } from "lucide-react";
import { useUsers } from "@/modules/users/hooks/use-users";
import { useProjects } from "@/modules/project/hooks/use-projects"; // Corrected import
import { useState, useEffect } from "react";

interface User {
  sub: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  roles?: string[];
}

interface InventoryHeaderProps {
  isPick: boolean;
  setIsPick: (isPick: boolean) => void;
  selectedUserId?: string;
  selectedProjectId?: string;
  onUserChange?: (userId: string) => void;
  onProjectChange?: (projectId: string) => void;
}

const InventoryHeader = ({
  isPick,
  setIsPick,
  selectedUserId,
  selectedProjectId,
  onUserChange,
  onProjectChange,
}: InventoryHeaderProps) => {
  const { data: users, isLoading } = useUsers();
  const { data: projects, isLoading: isProjectsLoading } = useProjects();

  // Get current user and permissions
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userPermissions, setUserPermissions] = useState<string[]>([]);

  useEffect(() => {
    // Get current user from localStorage
    const storedUser = localStorage.getItem("user");
    const storedPermissions = localStorage.getItem("permissions");
    if (storedUser) {
      try {
        setCurrentUser(JSON.parse(storedUser));
      } catch (error) {
        console.error("Error parsing user data:", error);
      }
    }
    // Get user permissions from localStorage
    if (storedPermissions) {
      try {
        const permissions = JSON.parse(storedPermissions);
        setUserPermissions(Array.isArray(permissions) ? permissions : []);
      } catch (error) {
        console.error("Error parsing permissions:", error);
        setUserPermissions([]);
      }
    }
  }, []);

  const canPickForOthers = userPermissions.includes("pick-for-others");

  const currentUserFromList = users?.find(
    (user) => user.id === currentUser?.sub
  );

  const effectiveSelectedUserId = canPickForOthers
    ? selectedUserId || currentUserFromList?.id
    : currentUserFromList?.id || selectedUserId;

  // Notify parent component when effectiveSelectedUserId changes
  useEffect(() => {
    if (effectiveSelectedUserId && effectiveSelectedUserId !== selectedUserId) {
      onUserChange?.(effectiveSelectedUserId);
    }
  }, [effectiveSelectedUserId, selectedUserId, onUserChange]);

  const effectiveSelectedUser = users?.find(
    (user) => user.id === effectiveSelectedUserId
  );

  // Handle user selection with permission check
  const handleUserChange = (userId: string) => {
    if (canPickForOthers) {
      // User has permission, allow any selection
      onUserChange?.(userId);
    } else {
      // User doesn't have permission, only allow current user selection
      if (userId === currentUserFromList?.id) {
        onUserChange?.(userId);
      }
      // If trying to select someone else, ignore the change
    }
  };

  return (
    <div className="flex items-center justify-between px-10 py-6 bg-white ">
      <div className="relative flex items-center border p-1  rounded-lg h-14 w-[30%]">
        <div
          className={`absolute left-0 h-[70%] w-[45%] bg-blue-500 rounded transition-transform duration-300 ease-in-out  ${
            isPick ? "translate-x-2" : "translate-x-[118%]"
          }`}
        />
        <button
          onClick={() => setIsPick(true)}
          disabled={!canPickForOthers && !isPick}
          className={`relative z-10 w-1/2 bg-transparent hover:bg-transparent font-semibold flex items-center justify-center gap-2 ${
            isPick
              ? "text-white"
              : !canPickForOthers
              ? "text-gray-400 cursor-not-allowed"
              : "text-black"
          }`}
        >
          <Plus /> Pick Items
        </button>
        <button
          onClick={() => setIsPick(false)}
          className={`relative z-10 w-1/2 bg-transparent hover:bg-transparent font-semibold flex items-center justify-center gap-2 ${
            !isPick ? "text-white" : "text-black"
          }`}
        >
          <Minus /> Drop Items
        </button>
      </div>
      <div className="flex items-center justify-end space-x-4 w-1/2">
        <span className="font-semibold text-xl">
          {isPick ? "Pick For" : "Drop For"}
        </span>
        <Select
          value={effectiveSelectedUserId}
          onValueChange={handleUserChange}
          disabled={!canPickForOthers}
        >
          <SelectTrigger className="w-[180px] !h-11">
            <SelectValue>
              {isLoading
                ? "Loading users..."
                : effectiveSelectedUser
                ? `${effectiveSelectedUser.firstName} ${effectiveSelectedUser.lastName}`
                : canPickForOthers
                ? "Select a user"
                : "Current User"}
            </SelectValue>
          </SelectTrigger>
          {canPickForOthers && (
            <SelectContent>
              {users?.map((user) => (
                <SelectItem key={user.id} value={user.id}>
                  {`${user.firstName} ${user.lastName}`}
                </SelectItem>
              ))}
            </SelectContent>
          )}
        </Select>
        {isPick && (
          <Select value={selectedProjectId} onValueChange={onProjectChange}>
            <SelectTrigger className="w-[180px] !h-11">
              <SelectValue
                placeholder={
                  isProjectsLoading ? "Loading projects..." : "Select a project"
                }
              />
            </SelectTrigger>
            <SelectContent>
              {projects?.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.projectName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  );
};

export default InventoryHeader;
