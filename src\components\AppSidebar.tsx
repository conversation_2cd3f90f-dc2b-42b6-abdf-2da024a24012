import { cn } from "@/lib/utils";
import {
  LayoutGrid,
  Package2,
  Boxes,
  Users,
  ScrollText,
  File,
} from "lucide-react";
import { NavLink } from "react-router-dom";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { SidebarMenu, SidebarMenuItem } from "./ui/sidebar";

const sidebarItems = [
  {
    title: "Dashboard",
    icon: LayoutGrid,
    href: "/",
  },
  {
    title: "Resources",
    icon: Package2,
    href: "/resources",
  },
  {
    title: "Inventory",
    icon: Boxes,
    href: "/inventory",
  },
  {
    title: "Users",
    icon: Users,
    href: "/users",
  },
  {
    title: "Suppliers",
    icon: Users,
    href: "/suppliers",
  },
  {
    title: "Projects",
    icon: File,
    href: "/projects",
  },
  {
    title: "Reports",
    icon: ScrollText,
    href: "/reports",
  },
  
];

const AppSidebar = () => {
  return (
    <TooltipProvider delayDuration={0}>
      <div className="flex h-screen w-[6.5rem] flex-col justify-between border-r bg-[#002437] px-2 py-4">
        <div className="flex flex-col items-center gap-y-4">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg mb-6">
            <Package2 className="h-6 w-6 text-white" />
          </div>
          <nav className="flex flex-col items-center gap-y-4">
            {sidebarItems.map((item) => (
              <Tooltip key={item.href}>
                <TooltipTrigger asChild>
                  <SidebarMenu>
                    <SidebarMenuItem>
                      <NavLink
                        to={item.href}
                        className={({ isActive }) =>
                          cn(
                            "flex h-12 w-12 items-center justify-center rounded-full transition-colors hover:bg-[#01A3E5]",
                            isActive && "bg-[#01A3E5]"
                          )
                        }
                      >
                        <item.icon className="h-6 w-6 text-white" />
                      </NavLink>
                    </SidebarMenuItem>
                  </SidebarMenu>
                </TooltipTrigger>
                <TooltipContent
                  side="right"
                  className="font-medium h-10 flex items-center"
                >
                  {item.title}
                </TooltipContent>
              </Tooltip>
            ))}
          </nav>
        </div>
      </div>
    </TooltipProvider>
  );
};

export default AppSidebar;
