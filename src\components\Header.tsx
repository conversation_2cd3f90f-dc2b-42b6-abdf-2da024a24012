import { Bell, ChevronDown } from "lucide-react";
import { Badge } from "./ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";

interface HeaderProps {
  title?: string;
}

const Header = ({ title = "Dashboard" }: HeaderProps) => {
  const [user, setUser] = useState<{ firstName?: string; lastName?: string; email?: string; roles?: string[] } | null>(null);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch {
        setUser(null);
      }
    } else {
      setUser(null);
    }
  }, []);

  const handleSignOut = () => {
    localStorage.removeItem("token");
    localStorage.removeItem("user");
    localStorage.removeItem("permissions");
    localStorage.removeItem("userId");
    localStorage.removeItem("temp_token");
    setUser(null);
    setDropdownOpen(false);
    navigate("/login");
  };

  const handleSignIn = () => {
    setDropdownOpen(false);
    navigate("/login");
  };

  return (
    <header className="flex items-center justify-between py-3 px-10 border-b bg-white">
      <h1 className="text-xl font-semibold text-headingBlack">{title}</h1>
      <div className="flex items-center gap-6">
        <button className="relative p-2 rounded-full hover:bg-gray-100 focus:outline-none">
          <Bell className="w-6 h-6 text-bellBlack" />
        </button>
        {user ? (
          <div className="flex items-center gap-2 relative">
            <Badge className="bg-roleBgBlue text-roleTextBlue text-xs font-medium px-3 py-1.5 rounded-full">
                {user.roles && user.roles.length > 0 ? user.roles[0].charAt(0).toUpperCase() + user.roles[0].slice(1) : "User"}
            </Badge>
            <span className="font-semibold text-black">
              {user.firstName || "User"} {user.lastName || ""}
            </span>
            <Avatar className="w-12 h-12 border-4 border-avatarBorderBlue">
              <AvatarImage src="https://randomuser.me/api/portraits/men/32.jpg" alt={user.firstName || "User"} />
              <AvatarFallback>{user.firstName ? user.firstName[0] : "U"}{user.lastName ? user.lastName[0] : ""}</AvatarFallback>
            </Avatar>
            <button type="button" onClick={() => setDropdownOpen((open) => !open)} className="ml-1">
              <ChevronDown className="w-4 h-4 text-dropArrow" />
            </button>
            {dropdownOpen && (
              <div className="absolute right-0 top-full mt-2 w-40 bg-white border rounded shadow-lg z-10">
                <button
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 text-red-500"
                  onClick={handleSignOut}
                >
                  Sign Out
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="relative">
            <button type="button" onClick={() => setDropdownOpen((open) => !open)} className="ml-1">
              <ChevronDown className="w-4 h-4 text-dropArrow" />
            </button>
            {dropdownOpen && (
              <div className="absolute right-0 top-full mt-2 w-40 bg-white border rounded shadow-lg z-10">
                <button
                  className="w-full text-left px-4 py-2 hover:bg-gray-100 text-primary-500"
                  onClick={handleSignIn}
                >
                  Sign In
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;