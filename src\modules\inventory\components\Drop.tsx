import PickDropList from "./PickDropList";
import PickedItemsToDrop from "./PickedItemsToDrop";
import type { Transaction } from "@/types/inventory";
import { useState } from "react";
import type { CheckoutItem } from "./Pick";

interface DropProps {
  userId?: string;
}

const Drop = ({ userId }: DropProps) => {
  const [checkoutItems, setCheckoutItems] = useState<CheckoutItem[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleRefreshData = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleDropItem = (item: Transaction) => {
    setCheckoutItems((prev) => {
      const existingItemIndex = prev.findIndex(
        (checkoutItem) => checkoutItem.id === item.resourceId
      );

      if (existingItemIndex >= 0) {
        // If item exists, increase quantity (up to available quantity)
        const existingItem = prev[existingItemIndex];
        if (existingItem.quantity < existingItem.maxQuantity) {
          const newItems = [...prev];
          newItems[existingItemIndex] = {
            ...existingItem,
            quantity: existingItem.quantity + 1,
          };
          return newItems;
        }
        return prev; // Don't add if already at max
      } else {
        // Add new item to checkout
        const newItem: CheckoutItem = {
          id: item.resourceId,
          name: item.resourceName,
          imageUrl: item.resourceImageUrl,
          type:
            item.unitType.toLowerCase() === "consumable"
              ? "consumable"
              : "asset",
          quantity: 1,
          maxQuantity: item.effectiveQuantity,
          projectId: item.project?.id, // Add projectId from item data
          pickId: item.id, // Add pickId (transaction ID) for drop operations
        };
        return [...prev, newItem];
      }
    });
  };

  const handleUpdateCheckoutItem = (id: string, quantity: number) => {
    setCheckoutItems((prevItems) =>
      prevItems.map((item) =>
        item.id === id ? { ...item, quantity } : item
      )
    );
  };

  const handleRemoveCheckoutItem = (id: string) => {
    setCheckoutItems((prevItems) =>
      prevItems.filter((item) => item.id !== id)
    );
  };

  const handleRemoveFromCheckout = (item: Transaction) => {
    setCheckoutItems((prev) => 
      prev.filter((checkoutItem) => checkoutItem.id !== item.resourceId)
    );
  };

  const handleClearCheckoutItems = () => {
    setCheckoutItems([]);
  };

  return (
    <div className="flex gap-6 p-6 bg-[#FCFCFC]">
      <PickedItemsToDrop 
        onDrop={handleDropItem} 
        onRemoveFromCheckout={handleRemoveFromCheckout}
        refreshTrigger={refreshTrigger} 
        checkoutItems={checkoutItems}
      />
      <PickDropList
        items={checkoutItems}
        onUpdateItem={handleUpdateCheckoutItem}
        onRemoveItem={handleRemoveCheckoutItem}
        onClearItems={handleClearCheckoutItems}
        onRefreshData={handleRefreshData}
        type="drop"
        userId={userId}
      />
    </div>
  );
};

export default Drop;
