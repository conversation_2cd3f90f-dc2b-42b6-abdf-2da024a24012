import type { ResourceType, UnitType } from "./enums";

export interface Resource {
  id: string;
  unitType: UnitType;
  name: string;
  resourceCode: string;
  unitSize: number;
  imageUrl?: string;
  resourceCategory: string;
  minInventoryQty: number;
  maxInventoryQty: number;
  resourceType: ResourceType;
  deletedAt?: string;
  createdAt?: string;
  updatedAt?: string;
  
  // New fields from updated API response
  totalAvailableQuantity: number;
  averagePricePerUnit: number;
  
  // Computed properties that might be needed for the UI
  quantity?: number;
  lowStock?: boolean;
}

export type ResourceFormData = Partial<Resource>;

export interface StockUpdateData {
  date: string;
  location: string;
  supplierName: string;
  supplierCode: string;
  supplierId: string;
  supplierResourceCode: string;
  unitPrice: number;
  quantity: number;
}

export interface InventoryData {
  supplierResourceCode: string;
  resourceId: string;
  supplierId: string;
  quantity: number;
  pricePerUnit: number;
  unitType: UnitType;
  userId: string;
  location: string;
  addedOn: string;
}
