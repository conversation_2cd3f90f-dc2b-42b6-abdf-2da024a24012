import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { CheckCircle } from "lucide-react";

const PasswordResetSuccessPage = () => {
  const navigate = useNavigate();

  const handleLoginClick = () => {
    navigate("/login");
  };

  return (
    <div className="min-h-screen bg-[#FCFCFC] flex flex-col items-center justify-center px-4 relative">
      <img src="/bgLogoLeft.svg" alt="" className="absolute top-10 left-0" />
      <img src="/bgLogoRight.svg" alt="" className="absolute top-0 right-0" />
      <div className="w-full max-w-sm flex-grow flex items-center justify-center">
        <div className="text-center">
          {/* Logo/Brand */}
          <div className="text-center mb-8">
            <img src="/fontLogo.svg" alt="PickTrail Logo" className="mx-auto" />
          </div>

          {/* Success Content */}
          <div className="p-8">
            <div className="text-center mb-8">
              {/* Success Icon */}
              <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-green-100">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>

              <h2 className="text-2xl font-semibold text-gray-900 mb-4">
                Password Reset Successful!
              </h2>
              <p className="text-sm text-gray-500 leading-relaxed">
                Your password has been successfully updated. You can now login with your new password.
              </p>
            </div>

            {/* Login Button */}
            <Button
              onClick={handleLoginClick}
              className="w-full bg-primary-500 hover:bg-primary-600 text-white font-medium h-11 text-base"
            >
              Click to Login
            </Button>
          </div>
        </div>
      </div>

      {/* Copyright */}
      <div className="flex w-full text-left mb-14 ml-20">
        <p className="text-xs text-grey-900 font-medium">
          © 2025 PickTrail. All rights reserved.
        </p>
      </div>
    </div>
  );
};

export default PasswordResetSuccessPage;
