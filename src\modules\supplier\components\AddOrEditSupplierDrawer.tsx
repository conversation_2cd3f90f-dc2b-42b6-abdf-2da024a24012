import { useState, useEffect } from "react";
import { X } from "lucide-react";
import { z } from "zod";
import toast from "react-hot-toast";
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetDescription,
} from "@/components/ui/sheet";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "../../../components/ui/textarea";
import type { Supplier, SupplierFormData } from "@/types/supplier";
import { createSupplier, updateSupplier } from "@/services/api";

const supplierSchema = z.object({
  name: z.string().min(1, "Supplier name is required"),
  code: z.string().min(1, "Company code is required"),
  contactPerson: z.string().min(1, "Contact person is required"),
  phoneNumber: z.string().min(1, "Phone number is required"),
  contactEmail: z.string().email("Invalid email address"),
  address: z.string().min(1, "Address is required"),
});

interface AddOrEditSupplierDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  supplier?: Supplier | null;
  onSupplierUpdate: () => void;
}

const AddOrEditSupplierDrawer = ({
  open,
  onOpenChange,
  supplier,
  onSupplierUpdate,
}: AddOrEditSupplierDrawerProps) => {
  const [formData, setFormData] = useState<SupplierFormData>({
    name: "",
    code: "",
    contactPerson: "",
    phoneNumber: "",
    contactEmail: "",
    address: "",
  });
  const [errors, setErrors] = useState<z.ZodError | null>(null);

  const isEditMode = !!supplier;

  useEffect(() => {
    if (isEditMode && supplier) {
      setFormData({
        name: supplier.name,
        code: supplier.code,
        contactPerson: supplier.contactPerson,
        phoneNumber: supplier.phoneNumber,
        contactEmail: supplier.contactEmail,
        address: supplier.address,
      });
    } else {
      // Reset form for add mode
      setFormData({
        name: "",
        code: "",
        contactPerson: "",
        phoneNumber: "",
        contactEmail: "",
        address: "",
      });
    }
  }, [supplier, isEditMode]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const result = supplierSchema.safeParse(formData);

    if (!result.success) {
      setErrors(result.error);
      toast.error("Please fix the errors in the form");
      return;
    }

    setErrors(null);
    const supplierData = result.data;

    try {
      if (isEditMode && supplier) {
        await updateSupplier(supplier.id, supplierData);
        toast.success("Supplier updated successfully");
      } else {
        await createSupplier(supplierData);
        toast.success("Supplier created successfully");
      }
      onSupplierUpdate();
      onOpenChange(false);
    } catch (error) {
      console.error("Failed to save supplier:", error);
      toast.error("Failed to save supplier");
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:max-w-[30vw]  [&>button]:hidden p-10">
        <div className="flex flex-col h-full w-full  ">
          {/* Custom Close Button */}
          <div className=" pb-0">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="w-fit p-2 bg-gray-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Header */}
          <SheetHeader className="mt-12  p-0">
            <SheetTitle className="text-2xl font-semibold text-gray-900 ">
              {isEditMode ? "Edit Supplier" : "Add New Supplier"}
            </SheetTitle>
            <SheetDescription className="text-sm text-gray-600 ">
              {isEditMode
                ? "Update the details of this supplier."
                : "Add a new supplier to associate with your resources."}
            </SheetDescription>
          </SheetHeader>

          {/* Form Content */}
          <div className="pb-6  mt-10 px-1">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="name">Supplier Name</Label>
                <Input
                  id="name"
                  placeholder="Enter supplier name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className="h-11"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">Supplier code</Label>
                <Input
                  id="code"
                  placeholder="Enter supplier code"
                  value={formData.code}
                  onChange={(e) =>
                    handleInputChange("code", e.target.value)
                  }
                  className="h-11"
                />
              </div>
              <h3 className="text-lg font-medium">CONTACT DETAILS</h3>
              <div className="space-y-2">
                <Label htmlFor="contactPerson">Contact person</Label>
                <Input
                  id="contactPerson"
                  placeholder="Enter contact person name"
                  value={formData.contactPerson}
                  onChange={(e) =>
                    handleInputChange("contactPerson", e.target.value)
                  }
                  className="h-11"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phoneNumber">Phone number</Label>
                <Input
                  id="phoneNumber"
                  placeholder="+61 0000 0000 00"
                  value={formData.phoneNumber}
                  onChange={(e) =>
                    handleInputChange("phoneNumber", e.target.value)
                  }
                  className="h-11"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactEmail">Email</Label>
                <Input
                  id="contactEmail"
                  placeholder="Enter email"
                  value={formData.contactEmail}
                  onChange={(e) => handleInputChange("contactEmail", e.target.value)}
                  className="h-11"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  placeholder="Enter address"
                  value={formData.address}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleInputChange("address", e.target.value)}
                />
              </div>

              {errors && (
                <div className="text-red-500">
                  <ul>
                    {errors.issues.map((err) => (
                      <li key={err.path.join(".")}>{err.message}</li>
                    ))}
                  </ul>
                </div>
              )}

              <div className="flex justify-between  gap-3 ">  
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  className="w-2/5 h-11"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-primary-500 hover:bg-primary-600 text-white  h-11 w-2/5"
                >
                  {isEditMode ? "Save Changes" : "Confirm"}
                </Button>
              </div>
            </form>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default AddOrEditSupplierDrawer;
