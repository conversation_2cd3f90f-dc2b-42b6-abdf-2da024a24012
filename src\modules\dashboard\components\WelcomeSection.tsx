import { CalendarDays } from "lucide-react";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface WelcomeSectionProps {
  onDateRangeChange: (startDate: string, endDate: string) => void;
}

const WelcomeSection = ({ onDateRangeChange }: WelcomeSectionProps) => {
  const [user, setUser] = useState<{ firstName?: string; lastName?: string } | null>(null);
  const [startDate, setStartDate] = useState<string>("");
  const [endDate, setEndDate] = useState<string>("");
  const [showDatePicker, setShowDatePicker] = useState(false);

  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser));
      } catch {
        setUser(null);
      }
    }

    // Set default date range (last 7 days)
    const today = new Date();
    const oneWeekAgo = new Date(today);
    oneWeekAgo.setDate(today.getDate() - 7);
    
    const formatDateForInput = (date: Date) => {
      return date.toISOString().split('T')[0];
    };
    
    const defaultStartDate = formatDateForInput(oneWeekAgo);
    const defaultEndDate = formatDateForInput(today);
    
    setStartDate(defaultStartDate);
    setEndDate(defaultEndDate);
    
    // Don't trigger initial load here - DashboardPage handles it
  }, []);

  const getCurrentDateRange = () => {
    if (!startDate || !endDate) return "Select date range";
    
    const formatDate = (dateStr: string) => {
      const date = new Date(dateStr);
      return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    };
    
    return `${formatDate(startDate)} - ${formatDate(endDate)}`;
  };

  const handleApplyFilter = () => {
    if (startDate && endDate) {
      onDateRangeChange(startDate, endDate);
      setShowDatePicker(false);
    }
  };

  const handleReset = () => {
    const today = new Date();
    const oneWeekAgo = new Date(today);
    oneWeekAgo.setDate(today.getDate() - 7);
    
    const formatDateForInput = (date: Date) => {
      return date.toISOString().split('T')[0];
    };
    
    const defaultStartDate = formatDateForInput(oneWeekAgo);
    const defaultEndDate = formatDateForInput(today);
    
    setStartDate(defaultStartDate);
    setEndDate(defaultEndDate);
    onDateRangeChange(defaultStartDate, defaultEndDate);
    setShowDatePicker(false);
  };

  return (
    <div className="flex items-center justify-between mb-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 mb-1">
          Welcome Back ! {user?.firstName || "John"} {user?.lastName || "Doe"}...
        </h1>
        <p className="text-gray-600">
          Here's a quick overview of your current projects and tasks.
        </p>
      </div>
      <div className="relative">
        <div 
          className="flex items-center gap-2 bg-white border rounded-lg px-4 py-2 cursor-pointer hover:bg-gray-50"
          onClick={() => setShowDatePicker(!showDatePicker)}
        >
          <span className="text-sm text-gray-600">{getCurrentDateRange()}</span>
          <CalendarDays className="w-4 h-4 text-blue-500" />
        </div>
        
        {showDatePicker && (
          <div className="absolute right-0 top-full mt-2 p-4 bg-white border rounded-lg shadow-lg z-10 min-w-[300px]">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex gap-2">
                <Button 
                  onClick={handleApplyFilter}
                  className="flex-1"
                  disabled={!startDate || !endDate}
                >
                  Apply Filter
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handleReset}
                  className="flex-1"
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default WelcomeSection;
