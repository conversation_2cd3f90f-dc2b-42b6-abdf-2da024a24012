import { Badge } from "@/components/ui/badge";
import type { Project } from "@/types/project";
import ProjectTable from "./ProjectTable";

interface ProjectsSectionProps {
  searchTerm: string;
  projects: Project[];
  refreshTrigger: number;
  onProjectsChange: (projects: Project[]) => void;
}

const ProjectsSection = ({
  searchTerm,
  projects,
  refreshTrigger,
  onProjectsChange,
}: ProjectsSectionProps) => {
  const filteredProjects = projects.filter(
    (project) =>
      project.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.projectCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <h2 className="text-lg font-medium">All Projects</h2>
        <Badge variant="secondary" className="bg-blue-100 text-blue-800">
          {filteredProjects.length}
        </Badge>
      </div>

      {/* Projects Table */}
      <ProjectTable
        searchTerm={searchTerm}
        refreshTrigger={refreshTrigger}
        onProjectsChange={onProjectsChange}
      />
    </div>
  );
};

export default ProjectsSection;
