
import { useEffect, useState } from "react";
import WelcomeSection from "./components/WelcomeSection";
import StatsCards from "./components/StatsCards";
import WeeklySummary from "./components/WeeklySummary";
import RecentPickAndDrop from "./components/RecentPickAndDrop";
import LowStockAlert from "./components/LowStockAlert";
import ProductCategories from "./components/ProductCategories";
import { getReportsSummary } from "../../services/api";
import type { CostReport } from "../../types/reports";

const DashboardPage = () => {
  const [reportData, setReportData] = useState<CostReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchReportData = async (startDate?: string, endDate?: string) => {
    try {
      setLoading(true);
      setError(null);
      const data = await getReportsSummary(startDate, endDate);
      setReportData(data);
    } catch (err) {
      console.error('Error fetching report data:', err);
      setError('Failed to fetch dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleDateRangeChange = (startDate: string, endDate: string) => {
    fetchReportData(startDate, endDate);
  };

  useEffect(() => {
    // Load initial data with default date range (last 7 days)
    const today = new Date();
    const oneWeekAgo = new Date(today);
    oneWeekAgo.setDate(today.getDate() - 7);
    
    const formatDateForAPI = (date: Date) => {
      return date.toISOString().split('T')[0];
    };
    
    const defaultStartDate = formatDateForAPI(oneWeekAgo);
    const defaultEndDate = formatDateForAPI(today);
    
    fetchReportData(defaultStartDate, defaultEndDate);
  }, []);

  if (loading) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <WelcomeSection onDateRangeChange={handleDateRangeChange} />
      <StatsCards reportData={reportData} />
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <WeeklySummary weeklyData={reportData?.weeklySummaries || []} />
        <RecentPickAndDrop />
      </div>
      
      <LowStockAlert />
      <ProductCategories 
        resourceCategoryCosts={reportData?.resourceCategoryCosts || []}
        projectCosts={reportData?.projectCosts || []}
      />
    </div>
  );
};

export default DashboardPage