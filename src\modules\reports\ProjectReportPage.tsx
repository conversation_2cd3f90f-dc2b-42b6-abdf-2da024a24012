import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import ProjectHeader from './components/ProjectHeader'
import ResourcesUsedTable, { type ResourceUsedItem } from './components/ResourcesUsedTable'

interface ProjectData {
  id: string
  projectName: string
  projectCode: string
  status: 'ACTIVE' | 'PAST' | 'UPCOMING'
  startDate: string
  dueDate: string
  totalAssets: number
  totalConsumables: number
  totalResources: number
  totalProjectCost: number
}

const ProjectReportPage = () => {
  const navigate = useNavigate()
  const { projectId } = useParams<{ projectId: string }>()
  
  const [projectData, setProjectData] = useState<ProjectData | null>(null)
  const [resourcesData, setResourcesData] = useState<ResourceUsedItem[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState('All Type')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Sample data - replace with actual API calls
  useEffect(() => {
    const fetchProjectData = async () => {
      try {
        setLoading(true)
        
        // Sample project data - replace with actual API call
        const sampleProject: ProjectData = {
          id: projectId || '1',
          projectName: 'Tsurium Pump',
          projectCode: '28049',
          status: 'ACTIVE',
          startDate: '12/07/2025',
          dueDate: '15/07/2025',
          totalAssets: 8,
          totalConsumables: 24,
          totalResources: 32,
          totalProjectCost: 4256.00
        }

        // Sample resources data - replace with actual API call
        const sampleResources: ResourceUsedItem[] = [
          {
            id: '1',
            itemName: '3m cubitron II 36G 125×22mm',
            itemCode: 'ABR0001',
            type: 'CONSUMABLE',
            unit: 'Individual',
            totalQuantityUsed: 200,
            imageUrl: 'https://picsum.photos/seed/item1/40/40'
          },
          {
            id: '2',
            itemName: '3m cubitron II 60G 125×22mm',
            itemCode: 'ABR0002',
            type: 'CONSUMABLE',
            unit: 'Individual',
            totalQuantityUsed: 32,
            imageUrl: 'https://picsum.photos/seed/item2/40/40'
          },
          {
            id: '3',
            itemName: 'Cutting disc 125mmX1.0X22mm',
            itemCode: 'ABR0003',
            type: 'ASSET',
            unit: 'Individual',
            totalQuantityUsed: 8,
            imageUrl: 'https://picsum.photos/seed/item3/40/40'
          },
          {
            id: '4',
            itemName: 'Abrasiflex 125×22mm flap disc',
            itemCode: 'ABR0004',
            type: 'CONSUMABLE',
            unit: 'Individual',
            totalQuantityUsed: 35,
            imageUrl: 'https://picsum.photos/seed/item4/40/40'
          },
          {
            id: '5',
            itemName: 'Abrasiflex 125×22mm 280 flap disk',
            itemCode: 'ABR0005',
            type: 'CONSUMABLE',
            unit: 'Individual',
            totalQuantityUsed: 453,
            imageUrl: 'https://picsum.photos/seed/item5/40/40'
          },
          {
            id: '6',
            itemName: 'Abrasiflex 100×16mm flap disk',
            itemCode: 'ABR0006',
            type: 'CONSUMABLE',
            unit: 'Individual',
            totalQuantityUsed: 234,
            imageUrl: 'https://picsum.photos/seed/item6/40/40'
          }
        ]

        setProjectData(sampleProject)
        setResourcesData(sampleResources)
      } catch (err) {
        console.error('Failed to fetch project data:', err)
        setError('Failed to load project data')
      } finally {
        setLoading(false)
      }
    }

    fetchProjectData()
  }, [projectId])

  const handleBack = () => {
    navigate('/reports')
  }

  const handleDownloadExcel = () => {
    // Implement Excel download functionality
    console.log('Downloading Excel file...')
  }

  // Filter resources based on search and type
  const filteredResources = resourcesData.filter((resource) => {
    const matchesSearch = searchTerm === '' || 
      resource.itemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.itemCode.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = typeFilter === 'All Type' || resource.type === typeFilter
    
    return matchesSearch && matchesType
  })

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <p>Loading project data...</p>
      </div>
    )
  }

  if (error || !projectData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <p className="text-red-500">{error || 'Project not found'}</p>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <ProjectHeader
        projectName={projectData.projectName}
        projectCode={projectData.projectCode}
        status={projectData.status}
        startDate={projectData.startDate}
        dueDate={projectData.dueDate}
        onBack={handleBack}
        totalAssets={projectData.totalAssets}
        totalConsumables={projectData.totalConsumables}
        totalResources={projectData.totalResources}
        totalProjectCost={projectData.totalProjectCost}
      />
      
      <div className="p-6 space-y-6">
        <ResourcesUsedTable
          data={filteredResources}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          typeFilter={typeFilter}
          onTypeFilterChange={setTypeFilter}
          onDownloadExcel={handleDownloadExcel}
        />
      </div>
    </div>
  )
}

export default ProjectReportPage