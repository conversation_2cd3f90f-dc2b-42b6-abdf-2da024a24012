"use client"

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import type { Supplier } from "@/types/supplier"
import { AlertTriangle, Store } from "lucide-react"

interface SupplierDeleteModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  supplier: Supplier | null
}

export function SupplierDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  supplier,
}: SupplierDeleteModalProps) {
  if (!supplier) {
    return null
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose} >
      <DialogContent className="sm:max-w-[425px] [&>button]:hidden p-10">
        <DialogHeader>
          <div className=" flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-6 w-6 text-red-600" aria-hidden="true" />
          </div>
          <DialogTitle className="mt-5 text-lg font-semibold">Delete supplier</DialogTitle>
          <DialogDescription className="">
            Are you sure you want to delete this supplier?
          </DialogDescription>
        </DialogHeader>
        <div className="my-8 h-[4.5rem] flex items-center rounded-md border border-blue-200 bg-blue-50 p-4">
          <div className="flex items-center gap-3">
            <Store className="h-6 w-6 text-blue-500" />
            <span className="font-medium">{supplier.name}</span>
          </div>
        </div>
        <DialogFooter className="">
          <button onClick={onClose} className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-transparent hover:bg-accent/5 text-primary h-10 px-4 py-2 w-1/2 border border-border">
            Cancel
          </button>
          <button onClick={onConfirm} className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-destructive hover:bg-destructive/90 text-destructive-foreground h-10 px-4 py-2 w-1/2 text-white">
            Delete
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
