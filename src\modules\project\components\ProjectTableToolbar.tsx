import React from "react";
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

export interface ProjectTableToolbarProps {
  searchTerm: string;
  setSearchTerm: (term: string) => void;
}

const ProjectTableToolbar: React.FC<ProjectTableToolbarProps> = ({
  searchTerm,
  setSearchTerm,
}) => {
  return (
    <div className="flex items-center justify-end">
      <div className="relative w-80">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search by project name or code..."
          className="pl-10"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
    </div>
  );
};

export default ProjectTableToolbar;
