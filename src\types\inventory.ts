export interface Project {
    id: string;
    projectCode: string;
    projectName: string;
}

export interface ChildTransaction {
    id: string;
    resourceName: string;
    resourceCategory: string;
    resourceImageUrl: string;
    resourceId: string;
    quantity: number;
    effectiveQuantity: number;
    unitType: string;
    actionType: string;
    userName: string;
    userId: string;
    timestamp: string;
    project: Project;
    pricePerUnit: string;
    childTransactions: ChildTransaction[];
}

export interface Transaction {
    id: string;
    resourceName: string;
    resourceCategory: string;
    resourceImageUrl: string;
    resourceId: string;
    quantity: number;
    effectiveQuantity: number;
    unitType: string;
    actionType: string;
    userName: string;
    userId: string;
    timestamp: string;
    project: Project;
    pricePerUnit: string;
    childTransactions: ChildTransaction[];
}

export interface InventoryItem {
    id: string;
    name: string;
    resourceCode: string;
    unitType: string;
    unitSize: number;
    imageUrl: string;
    resourceCategory: string;
    resourceType: string;
    totalAvailableQuantity: number;
    averagePricePerUnit: number;
    minInventoryQty: number;
    maxInventoryQty: number;
}
