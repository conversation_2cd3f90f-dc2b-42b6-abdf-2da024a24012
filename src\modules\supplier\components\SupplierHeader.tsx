import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search } from "lucide-react";
import AddOrEditSupplierDrawer from "./AddOrEditSupplierDrawer";

interface SupplierHeaderProps {
  searchTerm: string;
  onSearch: (term: string) => void;
  onSupplierUpdate: () => void;
}

const SupplierHeader = ({ searchTerm, onSearch, onSupplierUpdate }: SupplierHeaderProps) => {
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  return (
    <>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="scroll-m-20 text-2xl font-semibold tracking-tight mb-2">
            Manage your suppliers
          </h2>
          <p className="text-muted-foreground">
            Maintain and update supplier details to ensure seamless inventory operations.
          </p>
        </div>
        <Button
          className="bg-primary-500 hover:bg-primary-600 h-10 text-lg px-6 flex items-center justify-center"
          onClick={() => setIsDrawerOpen(true)}
        >
          <Plus className="h-6 w-6 text-white" />
          Add New Supplier
        </Button>
      </div>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400" />
        <Input
          placeholder="Search by item name or code..."
          className="pl-10"
          value={searchTerm}
          onChange={(e) => onSearch(e.target.value)}
        />
      </div>

      <AddOrEditSupplierDrawer
        open={isDrawerOpen}
        onOpenChange={setIsDrawerOpen}
        onSupplierUpdate={onSupplierUpdate}
      />
    </>
  );
};

export default SupplierHeader;
