import { useState } from "react";
import AvailableItemsForPick from "./AvailableItemsForPick";
import PickDropList from "./PickDropList";
import type { Transaction } from "@/types/inventory";

// Updated interface to match the new API response
interface InventoryItem {
  id: string;
  name: string;
  resourceCode: string;
  unitType: string;
  unitSize: number;
  imageUrl: string;
  resourceCategory: string;
  resourceType: string;
  totalAvailableQuantity: number;
  averagePricePerUnit: number;
  minInventoryQty: number;
  maxInventoryQty: number;
}

export interface CheckoutItem {
  id: string;
  name: string;
  imageUrl: string;
  type: "consumable" | "asset";
  quantity: number;
  maxQuantity: number;
  projectId?: string; // Add projectId for drop operations
  pickId?: string; // Add pickId (transaction ID) for drop operations
}

interface PickProps {
  userId?: string;
  projectId?: string;
}

const Pick = ({ userId, projectId }: PickProps) => {
  const [checkoutItems, setCheckoutItems] = useState<CheckoutItem[]>([]);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleRefreshData = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleAddToCheckout = (item: InventoryItem | Transaction) => {
    // Type guard to check if item is InventoryItem
    const isInventoryItem = (item: InventoryItem | Transaction): item is InventoryItem => {
      return 'id' in item;
    };

    // Extract properties based on item type
    const itemId = isInventoryItem(item) ? item.id : item.resourceId;
    const itemName = isInventoryItem(item) ? item.name : item.resourceName;
    const itemImage = isInventoryItem(item) ? item.imageUrl : item.resourceImageUrl;
    const itemQuantity = isInventoryItem(item) ? item.totalAvailableQuantity : item.effectiveQuantity;
    const itemUnitType = isInventoryItem(item) ? item.resourceType : item.unitType;

    setCheckoutItems((prev) => {
      const existingItemIndex = prev.findIndex(
        (checkoutItem) => checkoutItem.id === itemId
      );

      if (existingItemIndex >= 0) {
        // If item exists, increase quantity (up to available quantity)
        const existingItem = prev[existingItemIndex];
        if (existingItem.quantity < existingItem.maxQuantity) {
          const newItems = [...prev];
          newItems[existingItemIndex] = {
            ...existingItem,
            quantity: existingItem.quantity + 1,
          };
          return newItems;
        }
        return prev; // Don't add if already at max
      } else {
        // Add new item to checkout
        const newItem: CheckoutItem = {
          id: itemId,
          name: itemName,
          imageUrl: itemImage,
          type:
            itemUnitType.toLowerCase() === "consumable"
              ? "consumable"
              : "asset",
          quantity: 1,
          maxQuantity: itemQuantity,
        };
        return [...prev, newItem];
      }
    });
  };

  const handleUpdateCheckoutItem = (id: string, quantity: number) => {
    setCheckoutItems((prev) =>
      prev.map((item) =>
        item.id === id
          ? { ...item, quantity: Math.min(Math.max(0, quantity), item.maxQuantity) }
          : item
      )
    );
  };

  const handleRemoveCheckoutItem = (id: string) => {
    setCheckoutItems((prev) => prev.filter((item) => item.id !== id));
  };

  const handleClearCheckoutItems = () => {
    setCheckoutItems([]);
  };

  return (
    <div className="flex gap-6 p-6 bg-[#FCFCFC]">
      <AvailableItemsForPick 
        onAddToCheckout={handleAddToCheckout} 
        checkoutItems={checkoutItems}
        onRemoveFromCheckout={handleRemoveCheckoutItem}
        refreshTrigger={refreshTrigger}
      />
      <PickDropList
        items={checkoutItems}
        onUpdateItem={handleUpdateCheckoutItem}
        onRemoveItem={handleRemoveCheckoutItem}
        onClearItems={handleClearCheckoutItems}
        onRefreshData={handleRefreshData}
        type="pick"
        userId={userId}
        projectId={projectId}
      />
    </div>
  );
};

export default Pick;
