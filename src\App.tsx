import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import MainLayout from "./components/MainLayout";
import ResourceManagementPage from "./modules/resources/ResourceManagementPage";
import UserManagementPage from "./modules/users/UserManagementPage";
import SupplierManagementPage from "./modules/supplier/SupplierManagementPage";
import SupplierDetailsPage from "./modules/supplier/pages/SupplierDetailsPage";
import LoginPage from "./modules/auth/LoginPage";
import SetNewPasswordPage from "./modules/auth/SetNewPasswordPage";
import PasswordResetSuccessPage from "./modules/auth/PasswordResetSuccessPage";
import InventoryManagementPage from "./modules/inventory/InventoryManagementPage";
import ProjectManagementPage from "./modules/project/ProjectManagementPage";
import Reports from "./modules/reports/Reports";
import DashboardPage from "./modules/dashboard/DashboardPage";
import ProjectReportPage from "./modules/reports/ProjectReportPage";

const App = () => {
  return (
    <Router
      future={{
        v7_startTransition: true,
      }}
    >
      <div className="min-h-screen bg-gray-100">
        <Routes>
          {/* Login route - standalone without layout */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/reset-password" element={<SetNewPasswordPage />} />
          <Route path="/password-reset-success" element={<PasswordResetSuccessPage />} />

          {/* Protected routes with layout */}
          <Route path="/" element={<MainLayout />}>
            <Route index element={<DashboardPage />} />
            <Route path="/resources" element={<ResourceManagementPage />} />
            <Route path="/users" element={<UserManagementPage />} />
            <Route path="/suppliers" element={<SupplierManagementPage />} />
            <Route path="/suppliers/:id" element={<SupplierDetailsPage />} />
            <Route path="/inventory" element={<InventoryManagementPage />} />
            <Route path="/projects" element={<ProjectManagementPage />} />
            <Route path="/reports" element={<Reports />} />
            <Route path="/projects/:id" element={<ProjectReportPage />} />
          </Route>
        </Routes>
      </div>
    </Router>
  );
};

export default App;
