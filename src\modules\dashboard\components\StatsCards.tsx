import { DollarSign, Package, RotateCcw } from "lucide-react";
import type { CostReport } from "../../../types/reports";

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  bgColor: string;
  iconColor: string;
}

interface StatsCardsProps {
  reportData: CostReport | null;
}

const StatCard = ({ title, value, icon, bgColor, iconColor }: StatCardProps) => {
  return (
    <div className={`${bgColor} rounded-lg p-6 flex items-center gap-4`}>
      <div className={`${iconColor} p-3 rounded-full`}>
        {icon}
      </div>
      <div>
        <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
        <p className="text-2xl font-bold text-gray-900">{value}</p>
      </div>
    </div>
  );
};

const StatsCards = ({ reportData }: StatsCardsProps) => {
  // Calculate total consumable cost from all projects
  const totalConsumableCost = reportData?.projectCosts.reduce((total, project) => {
    return total + project.totalCost;
  }, 0) || 0;

  // Calculate number of items restocked (placeholder - you may need different API)
  const itemsRestocked = reportData?.projectCosts.length || 0;

  // Calculate pending returns (placeholder - you may need different API)
  const pendingReturns = 3; // This would come from a different API endpoint

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <StatCard
        title="Total Consumable Cost"
        value={`$${totalConsumableCost.toLocaleString()}`}
        icon={<DollarSign className="w-6 h-6 text-white" />}
        bgColor="bg-success-50 border border-success-200"
        iconColor="bg-green-500"
      />
      <StatCard
        title="Number of Items Restocked"
        value={itemsRestocked.toString()}
        icon={<Package className="w-6 h-6 text-white" />}
        bgColor="bg-blue-50 border border-blue-200"
        iconColor="bg-blue-500"
      />
      <StatCard
        title="Pending Returns"
        value={pendingReturns.toString()}
        icon={<RotateCcw className="w-6 h-6 text-white" />}
        bgColor="bg-red-50 border border-red-200"
        iconColor="bg-red-500"
      />
    </div>
  );
};

export default StatsCards;
