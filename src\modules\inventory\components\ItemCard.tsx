import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Minus, Plus, Trash2 } from "lucide-react";
import type { Transaction } from "@/types/inventory";

// Updated interface to match the new API response for inventory items
interface InventoryItem {
  id: string;
  name: string;
  resourceCode: string;
  unitType: string;
  unitSize: number;
  imageUrl: string;
  resourceCategory: string;
  resourceType: string;
  totalAvailableQuantity: number;
  averagePricePerUnit: number;
  minInventoryQty: number;
  maxInventoryQty: number;
}

interface ItemCardProps {
  item: InventoryItem | Transaction;
  onAddToCheckout?: (item: InventoryItem | Transaction) => void;
  onDrop?: (item: Transaction) => void;
  onRemove?: (item: InventoryItem | Transaction) => void;
  variant?: "pick" | "drop";
  isPicked?: boolean;
  isInCheckout?: boolean; // Add flag to indicate if item is in checkout list
}

const ItemCard = ({
  item,
  onAddToCheckout,
  onDrop,
  onRemove,
  variant = "pick",
  isPicked = false,
  isInCheckout = false,
}: ItemCardProps) => {
  // Helper functions to handle different item types
  const isInventoryItem = (item: InventoryItem | Transaction): item is InventoryItem => {
    return 'totalAvailableQuantity' in item;
  };

  const getItemId = () => isInventoryItem(item) ? item.id : item.resourceId;
  const getItemName = () => isInventoryItem(item) ? item.name : item.resourceName;
  const getItemImage = () => isInventoryItem(item) ? item.imageUrl : item.resourceImageUrl;
  const getItemQuantity = () => isInventoryItem(item) ? item.totalAvailableQuantity : item.effectiveQuantity;
  const getItemResourceType = () => isInventoryItem(item) ? item.resourceType : item.resourceCategory;
  const getItemCode = () => isInventoryItem(item) ? item.resourceCode : item.resourceId;

  const isOutOfStock = getItemQuantity() === 0;

  const handlePickClick = () => {
    if (!isOutOfStock && onAddToCheckout) {
      onAddToCheckout(item);
    }
  };

  const handleDropClick = () => {
    if (onDrop && !isInventoryItem(item)) {
      onDrop(item as Transaction);
    }
  };

  const handleRemoveClick = () => {
    if (onRemove) {
      onRemove(item);
    }
  };

  return (
    <div
      key={getItemId()}
      className={`flex items-center justify-between p-4 border rounded-lg ${
        isOutOfStock ? "border-red-500" : ""
      }`}
    >
      <div className="flex items-center gap-4">
        <img
          src={getItemImage()}
          alt={getItemName()}
          className="w-16 h-16 rounded-lg"
        />
        <div>
          <h3 className="font-semibold">{getItemName()}</h3>
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <span>{getItemCode()}</span>
            <span>|</span>
            <Badge
              variant={getItemResourceType() === "consumable" ? "default" : "secondary"}
              className={`px-2 py-1 w-fit rounded-lg font-semibold uppercase ${
                getItemResourceType() === 'asset'
                  ? 'bg-primary-100 text-primary-500'
                  : 'bg-warning-100 text-warning-500'
              }`}
            >
              {getItemResourceType()}
            </Badge>
            <span>|</span>
            <span>
              {variant === "pick" ? "Available" : "Picked"} Qty :{" "}
              <span className="text-green-500">{getItemQuantity()}</span>
            </span>
          </div>
        </div>
      </div>
      {variant === "pick" ? (
        isPicked ? (
          <Trash2 className="h-6 w-6 text-red-600 cursor-pointer hover:text-red-800" onClick={handleRemoveClick} />
        ) : (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Button disabled={isOutOfStock} className="bg-primary-100 text-primary-600 border-primary-600 border" onClick={handlePickClick}>
                  <Plus className=" h-4 w-4" />
                  {isOutOfStock ? "Out of Stock" : "Pick"}
                </Button>
              </TooltipTrigger>
              {isOutOfStock && (
                <TooltipContent>
                  <p>This item is out of stock.</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        )
      ) : (
        isInCheckout ? (
          <Trash2 className="h-6 w-6 text-red-600 cursor-pointer hover:text-red-800" onClick={handleRemoveClick} />
        ) : (
          <Button variant="outline" className="bg-primary-100 text-primary-600 border-primary-600 border" onClick={handleDropClick}>
            <Minus className="h-4 w-4" />
            Drop
          </Button>
        )
      )}
    </div>
  );
};

export default ItemCard;
