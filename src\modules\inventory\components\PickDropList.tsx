import EmptyState from "@/components/common/EmptyState";
import CheckoutListItem from "./CheckoutListItem";
import { Button } from "@/components/ui/button";
import ConfirmationModal from "@/components/common/ConfirmationModal";
import type { CheckoutItem } from "./Pick";
import { useState } from "react";
import { pickItems, dropItems } from "@/services/api";

interface PickDropListProps {
  items: CheckoutItem[];
  onUpdateItem: (id: string, quantity: number) => void;
  onRemoveItem: (id: string) => void;
  onClearItems: () => void; // Add callback to clear all items
  onRefreshData?: () => void; // Add callback to refresh available items
  type: "pick" | "drop";
  userId?: string;
  projectId?: string;
}

const PickDropList = ({
  items,
  onUpdateItem,
  onRemoveItem,
  onClearItems,
  onRefreshData,
  type,
  userId,
  projectId,
}: PickDropListProps) => {
  const [isConfirmationModalOpen, setIsConfirmationModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleIncrement = (id: string) => {
    const item = items.find((item) => item.id === id);
    if (item && item.quantity < item.maxQuantity) {
      onUpdateItem(id, item.quantity + 1);
    }
  };

  const handleDecrement = (id: string) => {
    const item = items.find((item) => item.id === id);
    if (item && item.quantity > 0) {
      onUpdateItem(id, item.quantity - 1);
    }
  };

  const handleQuantityChange = (id: string, quantity: number) => {
    onUpdateItem(id, quantity);
  };

  const handleConfirmAction = async () => {
    // For pick operations, require both userId and projectId
    // For drop operations, only require userId
    if (type === "pick" && (!userId || !projectId)) {
      console.error("Please select a user and project before proceeding");
      setIsConfirmationModalOpen(false);
      return;
    }
    
    if (type === "drop" && !userId) {
      console.error("Please select a user before proceeding");
      setIsConfirmationModalOpen(false);
      return;
    }

    // At this point, we know userId is defined for both cases
    // and for pick operations, projectId is also defined
    const validUserId = userId!;

    setIsLoading(true);
    try {
      if (type === "pick") {
        const validProjectId = projectId!;
        const pickData = {
          items: items.map(item => ({
            resourceId: item.id,
            quantity: item.quantity,
            unitType: "individual"
          })),
          userId: validUserId,
          projectId: validProjectId
        };
        
        await pickItems(pickData);
        console.log("Pick confirmed successfully");
      } else {
        // For drop operations, group items by projectId and make separate calls
        const itemsByProject = items.reduce((acc, item) => {
          const itemProjectId = item.projectId || "";
          if (!acc[itemProjectId]) {
            acc[itemProjectId] = [];
          }
          acc[itemProjectId].push(item);
          return acc;
        }, {} as Record<string, CheckoutItem[]>);

        // Make drop API calls for each project
        for (const [itemProjectId, projectItems] of Object.entries(itemsByProject)) {
          const dropData = {
            items: projectItems.map(item => ({
              resourceId: item.id,
              quantity: item.quantity,
              unitType: "individual"
            })),
            userId: validUserId,
            projectId: itemProjectId,
            pickId: projectItems[0]?.pickId // Include pickId (transaction ID) at the top level
          };
          
          await dropItems(dropData);
        }
        console.log("Drop confirmed successfully");
      }
      
      // Close modal and clear checkout items
      setIsConfirmationModalOpen(false);
      onClearItems(); // Clear all items from checkout list
      
      // Refresh the available items data to reflect the changes
      if (onRefreshData) {
        onRefreshData();
      }
      
      // TODO: You might want to add a success toast notification here
      
    } catch (error) {
      console.error(`Failed to ${type} items:`, error);
      // TODO: Show error message to user
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col justify-between w-full max-w-md border rounded-lg h-[76vh]">
      <div className="bg-grey-200 p-2 rounded-t-lg">
        <h2 className="text-xl font-bold text-center">
          {type === "pick" ? "Items Checkout List" : "Items Drop Summary"}
        </h2>
      </div>

      <div className="flex-1 p-4">
        {items.length === 0 ? (
          <div className="flex flex-col justify-center h-[60vh]">
            <EmptyState
              title="Looks like you haven't added anything yet."
              description="Add items to proceed."
              imageSrc="/emptyBox.svg"
            />
          </div>
        ) : (
          <div className="space-y-3 overflow-y-auto max-h-[60vh] h-full">
            {items.map((item) => (
              <CheckoutListItem
                key={item.id}
                name={item.name}
                imageUrl={item.imageUrl}
                type={item.type}
                quantity={item.quantity}
                onIncrement={() => handleIncrement(item.id)}
                onDecrement={() => handleDecrement(item.id)}
                onRemove={() => onRemoveItem(item.id)}
                onQuantityChange={(qty) => handleQuantityChange(item.id, qty)}
              />
            ))}
          </div>
        )}
      </div>
      <ConfirmationModal
        open={isConfirmationModalOpen}
        onOpenChange={setIsConfirmationModalOpen}
        title={type === "pick" ? "Confirm Pick" : "Confirm Drop"}
        description={
          type === "pick"
            ? "Are you sure want to pick these items?"
            : "Are you sure want to drop these items?"
        }
        onConfirm={handleConfirmAction}
        onCancel={() => setIsConfirmationModalOpen(false)}
      />
      {items.length > 0 && (
        <div className="p-4   rounded-b-lg">
          <div className="flex gap-3">
            <Button variant="outline" className="flex-1">
              Cancel
            </Button>
            <Button
              className="flex-1"
              onClick={() => setIsConfirmationModalOpen(true)}
              disabled={isLoading || (type === "pick" ? (!userId || !projectId) : !userId)}
            >
              {isLoading ? "Processing..." : (type === "pick" ? "Pick Items" : "Continue Drop")}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PickDropList;
