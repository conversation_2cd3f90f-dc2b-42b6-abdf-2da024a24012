import { useState, useEffect } from 'react'
import ProjectSummaryTable, { type ProjectData } from './components/ProjectSummaryTable'
import ReportsFilters from './components/ReportsFilters'
import ReportsHeader from './components/ReportsHeader'
import { getProjectConsumableCosts, type ProjectConsumableCostResponse } from '@/services/api'

const Reports = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [startDate, setStartDate] = useState('2025-05-25')
  const [endDate, setEndDate] = useState('2025-08-25')
  const [activeTab, setActiveTab] = useState<'project-summary' | 'product-category'>('project-summary')
  const [projectData, setProjectData] = useState<ProjectData[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch project consumable costs data
  useEffect(() => {
    const fetchProjectData = async () => {
      setLoading(true)
      setError(null)
      try {
        const data: ProjectConsumableCostResponse[] = await getProjectConsumableCosts(startDate, endDate)
        // Transform API response to match ProjectData interface
        const mappedData: ProjectData[] = data.map(item => ({
          projectCode: item.project.projectCode,
          projectName: item.project.projectName,
          startDate: new Date(item.project.startDate).toLocaleDateString('en-GB'),
          endDate: new Date(item.project.dueDate).toLocaleDateString('en-GB'),
          resources: item.resourcesCost.length,
          totalQuantity: item.resourcesCost.reduce((sum: number, resource) => sum + (resource.resource.unitSize || 0), 0),
          totalCost: `$${item.totalCost.toFixed(2)}`,
          status: 'ACTIVE' as const // Default status since API doesn't provide this
        }))
        setProjectData(mappedData)
      } catch (err) {
        console.error('Failed to fetch project data:', err)
        setError('Failed to load project data')
        // Set empty array if API fails
        setProjectData([])
      } finally {
        setLoading(false)
      }
    }

    fetchProjectData()
  }, [startDate, endDate])

  // Filter data based on search term only (status filter removed)
  const filteredData = projectData.filter((project) => {
    const matchesSearch = searchTerm === '' || 
      project.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.projectCode.toLowerCase().includes(searchTerm.toLowerCase())
    
    return matchesSearch
  })

  if (loading) {
    return (
      <div className="p-6">
        <ReportsHeader 
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
        <div className="flex justify-center items-center h-64">
          <p>Loading project data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <ReportsHeader 
          activeTab={activeTab}
          onTabChange={setActiveTab}
        />
        <div className="flex justify-center items-center h-64">
          <p className="text-red-500">{error}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 bg-[#fcfcfc] min-h-[92vh]">
      <ReportsHeader 
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />

      {/* Search and Filters */}
      <ReportsFilters
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        startDate={startDate}
        onStartDateChange={setStartDate}
        endDate={endDate}
        onEndDateChange={setEndDate}
      />

      {/* Data Table */}
      {filteredData.length > 0 ? (
        <ProjectSummaryTable data={filteredData} />
      ) : (
        <div className="flex justify-center items-center h-64 bg-white rounded-lg border">
          <p className="text-gray-500">No project data available for the selected date range.</p>
        </div>
      )}
    </div>
  )
}

export default Reports