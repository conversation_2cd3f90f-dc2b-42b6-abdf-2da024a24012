import { Search } from "lucide-react";
import { Button } from "@/components/ui/button";

interface EmptyStateProps {
  title: string;
  description: string;
  showSearchIcon?: boolean;
  imageSrc?: string;
  actionButton?: {
    label: string;
    onClick: () => void;
    variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  };
}

const EmptyState = ({
  title,
  description,
  showSearchIcon = false,
  imageSrc,
  actionButton,
}: EmptyStateProps) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-center">
      {imageSrc && (
        <img src={imageSrc} alt={title} className="w-48 h-48 mb-4" />
      )}
      {showSearchIcon && (
        <div className="mb-4 p-3 bg-slate-100 rounded-full">
          <Search className="h-8 w-8 text-slate-400" />
        </div>
      )}
      <h3 className="text-lg font-medium text-slate-900 mb-2">{title}</h3>
      <p className="text-sm text-slate-500 max-w-md mb-6">{description}</p>

      {actionButton && (
        <Button
          onClick={actionButton.onClick}
          variant={actionButton.variant || "outline"}
          className="mt-2"
        >
          {actionButton.label}
        </Button>
      )}
    </div>
  );
};

export default EmptyState;
