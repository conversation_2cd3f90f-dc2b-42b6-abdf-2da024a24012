import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import { jwtDecode } from "jwt-decode";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Eye, EyeOff } from "lucide-react";

export interface JWTPayload {
  roles: string[];
  sub: string; // subject (usually user id)
  iat: number; // issued at
  exp: number; // expiration time
  email: string;
}

const LoginPage = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<string[]>([]);
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors([]);

    try {
      const response = await axios.post("/api/auth/login", {
        email,
        password,
      });

      // Safely decode JWT to read claims
      const token = response.data.access_token;
      const decoded: JWTPayload = jwtDecode(token);

      // Normal login flow
      // Store authentication data
      localStorage.setItem("token", token);
      localStorage.setItem("user", JSON.stringify(decoded));

      // Get user permissions
      try {
        const permissionsResponse = await axios.get("/api/auth/authorization", {
          headers: { Authorization: `Bearer ${response.data.access_token}` },
        });
        if (permissionsResponse.data && permissionsResponse.data.permissions) {
          localStorage.setItem(
            "permissions",
            JSON.stringify(permissionsResponse.data.permissions)
          );

          // Check if user has "reports" permission
          const permissions = permissionsResponse.data.permissions;
          if (!permissions.includes("reports")) {
            navigate("/inventory");
            return;
          }
        }
      } catch (permissionError) {
        console.error("Failed to fetch permissions:", permissionError);
      }

      // Redirect all users to dashboard after login
      navigate("/");
    } catch (error: unknown) {
      const axiosError = error as { response?: { status: number; data: { access_token?: string } } };
      if (axiosError.response && axiosError.response.status === 402) {
        // Handle 402 status (First-time login, password change required)
        const token = axiosError.response.data.access_token;
        if (token) {
          const decoded: JWTPayload = jwtDecode(token);
          localStorage.setItem("temp_token", token);
          localStorage.setItem("userId", decoded.sub);

          // Try to get permissions even for first-time login
          try {
            const permissionsResponse = await axios.get(
              "/api/auth/authorization",
              {
                headers: { Authorization: `Bearer ${token}` },
              }
            );
            if (
              permissionsResponse.data &&
              permissionsResponse.data.permissions
            ) {
              localStorage.setItem(
                "permissions",
                JSON.stringify(permissionsResponse.data.permissions)
              );
            }
          } catch (permissionError) {
            console.error(
              "Failed to fetch permissions for new user:",
              permissionError
            );
          }

          navigate("/reset-password");
          return;
        }
      }
      setErrors(["Invalid email or password"]);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="min-h-screen bg-[#FCFCFC] flex flex-col items-center justify-center px-4 relative">
      <img src="/bgLogoLeft.svg" alt="" className="absolute top-0 left-0" />
      <img src="/bgLogoRight.svg" alt="" className="absolute top-0 right-0" />
      <div className="w-full max-w-[30rem] flex-grow flex items-center justify-center ">
        <div>
          {/* Logo/Brand */}
          <div className="text-center mb-8">
            <img src="/fontLogo.svg" alt="PickTrail Logo" className="mx-auto" />
          </div>

          {/* Login Form */}
          <div className="  p-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-semibold text-gray-900 mb-2">
                Welcome Back to Dustack
              </h2>
              <p className=" text-gray-500">
                Enter your credentials to continue
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field */}
              <div className="space-y-2">
                <Label
                  htmlFor="email"
                  className="text-sm font-medium text-grey-900"
                >
                  Email address
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  required
                  className="w-full h-11 text-grey-800"
                />
              </div>

              {/* Password Field */}
              <div className="space-y-2">
                <Label
                  htmlFor="password"
                  className="text-sm font-medium text-grey-900"
                >
                  Password
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    required
                    className="w-full h-11 pr-10 text-grey-800"
                  />
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                  </button>
                </div>
              </div>

              {/* Error Messages */}
              {errors.length > 0 && (
                <div className="space-y-2">
                  {errors.map((error, index) => (
                    <p key={index} className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                      {error}
                    </p>
                  ))}
                </div>
              )}

              {/* Login Button */}
              <Button
                type="submit"
                disabled={isLoading}
                className="w-full bg-primary-500 hover:bg-primary-600 text-white font-medium h-11 text-base mt-8"
              >
                {isLoading ? "Logging in..." : "Login"}
              </Button>
            </form>
          </div>
        </div>
      </div>
      {/* Copyright */}
      <div className="flex w-full text-left mb-14 ml-20">
        <p className="text-xs text-grey-900 font-medium">
          © 2025 PickTrail. All rights reserved.
        </p>
      </div>
    </div>
  );
};

export default LoginPage;
