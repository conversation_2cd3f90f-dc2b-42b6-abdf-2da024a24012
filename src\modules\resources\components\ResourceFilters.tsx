import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { ListFilter, Search, X } from "lucide-react";
import { useState, useCallback, useEffect, useRef } from "react";
import {
  RESOURCE_TYPE_OPTIONS,
  DEFAULT_FILTER_STATE,
  SEARCH_CONFIG,
} from "../config/resourceConfig";
import type { Resource } from "@/types/resource";

interface ResourceFiltersProps {
  resources: Resource[] | undefined;
  onFiltered: (filteredResources: Resource[]) => void;
  onClearFilters?: () => void;
}

const ResourceFilters = ({
  resources,
  onFiltered,
  onClearFilters,
}: ResourceFiltersProps) => {
  const [searchTerm, setSearchTerm] = useState<string>(
    DEFAULT_FILTER_STATE.searchTerm
  );
  const [selectedType, setSelectedType] = useState<string>(
    DEFAULT_FILTER_STATE.selectedType
  );
  const [showLowStock, setShowLowStock] = useState<boolean>(
    DEFAULT_FILTER_STATE.showLowStock
  );
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Keyboard shortcut to focus search input (Ctrl/Cmd + K)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.key === "k") {
        event.preventDefault();
        searchInputRef.current?.focus();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, []);

  // Filter and sort resources based on current filter state
  useEffect(() => {
    if (!resources || !Array.isArray(resources)) {
      onFiltered([]);
      return;
    }

    let filtered = [...resources];

    // Filter by search term (name or code)
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase().trim();
      filtered = filtered.filter((resource) =>
        SEARCH_CONFIG.searchFields.some((field) => {
          const value = resource[field];
          return (
            typeof value === "string" &&
            value.toLowerCase().includes(searchLower)
          );
        })
      );
    }

    // Filter by type
    if (selectedType !== "all") {
      filtered = filtered.filter((resource) => {
        return selectedType === "consumable" || selectedType === "asset"
          ? resource.resourceType === selectedType
          : true;
      });
    }

    // Sort by low stock if enabled
    if (showLowStock) {
      filtered.sort((a, b) => {
        const aQuantity = a.totalAvailableQuantity || 0;
        const bQuantity = b.totalAvailableQuantity || 0;
        const aMinQty = a.minInventoryQty || 0;
        const bMinQty = b.minInventoryQty || 0;

        const aLowStock = aQuantity <= aMinQty;
        const bLowStock = bQuantity <= bMinQty;

        if (aLowStock && !bLowStock) return -1;
        if (!aLowStock && bLowStock) return 1;
        return 0;
      });
    }

    onFiltered(filtered);
  }, [resources, searchTerm, selectedType, showLowStock, onFiltered]);

  const handleClearSearch = useCallback(() => {
    setSearchTerm("");
  }, []);

  const handleSearchInputChange = useCallback((value: string) => {
    setSearchTerm(value);
  }, []);

  const handleTypeChange = useCallback((value: string) => {
    setSelectedType(value);
  }, []);

  const handleLowStockToggle = useCallback((checked: boolean) => {
    setShowLowStock(checked);
  }, []);

  return (
    <section className="mt-10">
      <div className="flex items-center justify-between mb-6">
        <div className="relative w-[400px]">
          <Input
            ref={searchInputRef}
            type="text"
            placeholder={SEARCH_CONFIG.placeholder}
            className="pr-10"
            value={searchTerm}
            onChange={(e) => handleSearchInputChange(e.target.value)}
          />
          {searchTerm ? (
            <button
              className="absolute right-3 top-1/2 -translate-y-1/2"
              onClick={handleClearSearch}
            >
              <X className="h-5 w-5 text-slate-500 hover:text-slate-700" />
            </button>
          ) : (
            <button className="absolute right-3 top-1/2 -translate-y-1/2">
              <Search className="h-5 w-5 text-slate-500" />
            </button>
          )}
        </div>

        <div className="flex items-center gap-4">
          <div className={`flex items-center gap-2 border px-4 py-1.5 rounded-md ${(searchTerm.length > 0 || selectedType !== 'all' || showLowStock) ? 'bg-blue-200' : ''}`} onClick={() => { handleClearSearch(); handleTypeChange('all'); handleLowStockToggle(false); }}>
            <ListFilter className="h-5 w-5 text-slate-500" />
            Filter by
          </div>

          <Select value={selectedType} onValueChange={handleTypeChange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              {RESOURCE_TYPE_OPTIONS.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="flex items-center gap-3 mt-10 justify-between">
        <div className="flex items-center gap-3">
          <label
            htmlFor="lowStock"
            className="text-sm text-gray-600 select-none"
          >
            Show low stock quantity items on top
          </label>
          <Switch
            id="lowStock"
            className="data-[state=checked]:bg-[#03A3E5]"
            checked={showLowStock}
            onCheckedChange={handleLowStockToggle}
          />
        </div>
      </div>
    </section>
  );
};

export default ResourceFilters;
