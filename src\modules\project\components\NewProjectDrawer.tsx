import React, { useState } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>etDescription,
  She<PERSON>Footer,
} from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { CalendarIcon, X } from 'lucide-react'
import { createProject } from '@/services/api'
import type { ProjectFormData } from '@/types/project'

interface NewProjectDrawerProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onProjectCreated?: () => void
}

const NewProjectDrawer: React.FC<NewProjectDrawerProps> = ({ open, onOpenChange, onProjectCreated }) => {
  const [formData, setFormData] = useState({
    projectName: '',
    projectCode: '',
    startDate: '',
    dueDate: '',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleCancel = () => {
    onOpenChange(false)
    // Reset form data
    setFormData({
      projectName: '',
      projectCode: '',
      startDate: '',
      dueDate: '',
    })
  }

  const handleConfirm = async () => {
    // Basic form validation
    if (!formData.projectName.trim()) {
      setError('Project name is required')
      return
    }
    if (!formData.projectCode.trim()) {
      setError('Project code is required')
      return
    }
    if (!formData.startDate) {
      setError('Start date is required')
      return
    }
    if (!formData.dueDate) {
      setError('Due date is required')
      return
    }

    // Validate that due date is after start date
    if (new Date(formData.dueDate) <= new Date(formData.startDate)) {
      setError('Due date must be after start date')
      return
    }

    try {
      setIsLoading(true)
      setError(null)
      
      const projectData: ProjectFormData = {
        projectName: formData.projectName.trim(),
        projectCode: formData.projectCode.trim(),
        startDate: formData.startDate,
        dueDate: formData.dueDate,
      }

      await createProject(projectData)
      
      // Reset form data on success
      setFormData({
        projectName: '',
        projectCode: '',
        startDate: '',
        dueDate: '',
      })
      
      // Trigger refresh in parent component
      if (onProjectCreated) {
        onProjectCreated()
      }
      
      onOpenChange(false)
    } catch (err) {
      console.error('Error creating project:', err)
      setError('Failed to create project. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent side="right" className="[&>button]:hidden w-full sm:max-w-md p-10">
        <div className="pb-0">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenChange(false)}
            className="w-fit p-2 bg-gray-100"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <SheetHeader className="pb-6 pl-0">
          <SheetTitle>Start New Project</SheetTitle>
          <SheetDescription>
            Create a new project and keep your workflow organized.
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-sm text-red-600">{error}</p>
            </div>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="project-name">Project name</Label>
            <Input
              id="project-name"
              placeholder="Enter project name"
              value={formData.projectName}
              onChange={(e) => handleInputChange('projectName', e.target.value)}
              className="h-11"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="project-code">Project code</Label>
            <Input
              id="project-code"
              placeholder="Enter project code"
              value={formData.projectCode}
              onChange={(e) => handleInputChange('projectCode', e.target.value)}
              className="h-11"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="start-date">Start date</Label>
            <div className="relative">
              <Input
                id="start-date"
                type="date"
                placeholder="DD/MM/YYYY"
                value={formData.startDate}
                onChange={(e) => handleInputChange('startDate', e.target.value)}
                className="pr-10 h-11"
              />
              <CalendarIcon className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground pointer-events-none" />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="due-date">Due date</Label>
            <div className="relative">
              <Input
                id="due-date"
                type="date"
                placeholder="DD/MM/YYYY"
                value={formData.dueDate}
                onChange={(e) => handleInputChange('dueDate', e.target.value)}
                className="pr-10 h-11"
              />
              <CalendarIcon className="absolute right-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground pointer-events-none" />
            </div>
          </div>
        </div>

        <SheetFooter className="flex flex-row gap-3">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="flex-1"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            className="flex-1 bg-cyan-500 hover:bg-cyan-600 text-white"
            disabled={isLoading}
          >
            {isLoading ? 'Creating...' : 'Confirm'}
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  )
}

export default NewProjectDrawer