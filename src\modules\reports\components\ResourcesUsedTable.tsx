import { Search, Filter, Download } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/DataTable'
import { type ColumnDef } from '@tanstack/react-table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

export interface ResourceUsedItem {
  id: string
  itemName: string
  itemCode: string
  type: 'CONSUMABLE' | 'ASSET'
  unit: string
  totalQuantityUsed: number
  imageUrl?: string
}

interface ResourcesUsedTableProps {
  data: ResourceUsedItem[]
  searchTerm: string
  onSearchChange: (value: string) => void
  typeFilter: string
  onTypeFilterChange: (value: string) => void
  onDownloadExcel: () => void
}

const ResourcesUsedTable = ({
  data,
  searchTerm,
  onSearchChange,
  typeFilter,
  onTypeFilterChange,
  onDownloadExcel
}: ResourcesUsedTableProps) => {
  const getTypeBadgeVariant = (type: string) => {
    switch (type) {
      case 'CONSUMABLE':
        return 'default' // Orange
      case 'ASSET':
        return 'secondary' // Blue
      default:
        return 'default'
    }
  }

  const columns: ColumnDef<ResourceUsedItem>[] = [
    {
      accessorKey: 'itemName',
      header: 'ITEM NAME',
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <img 
            src={row.original.imageUrl || '/placeholder-image.jpg'} 
            alt={row.original.itemName}
            className="w-10 h-10 rounded-lg object-cover"
          />
          <span className="font-medium">{row.original.itemName}</span>
        </div>
      ),
    },
    {
      accessorKey: 'itemCode',
      header: 'ITEM CODE',
      cell: ({ getValue }) => (
        <span className="text-gray-600">{getValue() as string}</span>
      ),
    },
    {
      accessorKey: 'type',
      header: 'TYPE',
      cell: ({ getValue }) => (
        <Badge variant={getTypeBadgeVariant(getValue() as string)}>
          {getValue() as string}
        </Badge>
      ),
    },
    {
      accessorKey: 'unit',
      header: 'UNIT',
      cell: ({ getValue }) => (
        <span className="text-gray-600">{getValue() as string}</span>
      ),
    },
    {
      accessorKey: 'totalQuantityUsed',
      header: 'TOTAL QUANTITY USED',
      cell: ({ getValue }) => (
        <span className="font-medium">{getValue() as number}</span>
      ),
    },
  ]

  return (
    <div className="bg-white rounded-lg">
      <div className="p-6 border-b">
        <h2 className="text-lg font-semibold text-gray-900 mb-2">Resources Used</h2>
        <p className="text-sm text-gray-500 mb-6">All assets and consumables used for this project</p>
        
        <div className="flex flex-col md:flex-row gap-4 justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by item name or code..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-3">
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filter by
            </Button>
            
            <Select value={typeFilter} onValueChange={onTypeFilterChange}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="All Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All Type">All Type</SelectItem>
                <SelectItem value="CONSUMABLE">Consumable</SelectItem>
                <SelectItem value="ASSET">Asset</SelectItem>
              </SelectContent>
            </Select>
            
            <Button 
              onClick={onDownloadExcel}
              className="bg-blue-500 hover:bg-blue-600 text-white flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Download as Excel
            </Button>
          </div>
        </div>
      </div>

      <div className="p-6">
        <DataTable columns={columns} data={data} />
      </div>
    </div>
  )
}

export default ResourcesUsedTable
