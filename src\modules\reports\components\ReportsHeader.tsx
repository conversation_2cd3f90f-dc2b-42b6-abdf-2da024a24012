import { Button } from '@/components/ui/button'

interface ReportsHeaderProps {
  activeTab: 'project-summary' | 'product-category'
  onTabChange: (tab: 'project-summary' | 'product-category') => void
}

const ReportsHeader = ({ activeTab, onTabChange }: ReportsHeaderProps) => {
  return (
    <>
      {/* Header Tabs */}
      <div className="flex gap-4 mb-6">
        <Button 
          className={activeTab === 'project-summary' ? "bg-blue-500 hover:bg-blue-600 text-white" : ""}
          variant={activeTab === 'project-summary' ? "default" : "outline"}
          onClick={() => onTabChange('project-summary')}
        >
          Project Summary Report
        </Button>
        <Button 
          variant={activeTab === 'product-category' ? "default" : "outline"}
          className={activeTab === 'product-category' ? "bg-blue-500 hover:bg-blue-600 text-white" : "text-gray-600"}
          onClick={() => onTabChange('product-category')}
        >
          Product Category report
        </Button>
      </div>

      {/* Title */}
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-gray-900 mb-2">
          {activeTab === 'project-summary' ? 'Project Summary Report' : 'Product Category Report'}
        </h1>
        <p className="text-gray-600">
          {activeTab === 'project-summary' 
            ? 'Summarized insights to keep your projects on track'
            : 'Categorized insights to track your product inventory'
          }
        </p>
      </div>
    </>
  )
}

export default ReportsHeader
