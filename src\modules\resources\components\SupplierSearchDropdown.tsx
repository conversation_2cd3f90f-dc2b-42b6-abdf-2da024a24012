import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Check, ChevronsUpDown, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { useSuppliers } from "@/modules/supplier/hooks/use-suppliers";
import type { Supplier } from "@/types/supplier";

interface SupplierSearchDropdownProps {
  value: string;
  onChange: (supplierName: string, supplierCode: string, supplierId: string) => void;
  placeholder?: string;
  label?: string;
  className?: string;
}

const SupplierSearchDropdown = ({
  value,
  onChange,
  placeholder = "Select Supplier",
  label = "Supplier Name",
  className,
}: SupplierSearchDropdownProps) => {
  const { data: suppliers, isLoading: isLoadingSuppliers } = useSuppliers();
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");
  const navigate = useNavigate();

  const filteredSuppliers =
    suppliers?.filter((supplier: Supplier) =>
      supplier.name.toLowerCase().includes(searchValue.toLowerCase())
    ) || [];

  const handleSupplierSelect = (supplierName: string) => {
    const selectedSupplier = suppliers?.find(
      (s: Supplier) => s.name === supplierName
    );
    onChange(
      supplierName, 
      selectedSupplier ? selectedSupplier.code : "",
      selectedSupplier ? selectedSupplier.id : ""
    );
    setSearchValue("");
    setSearchOpen(false);
  };

  const handleAddSupplier = () => {
    navigate("/suppliers");
    setSearchOpen(false);
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Label
        htmlFor="supplierName"
        className="text-sm font-medium text-gray-700"
      >
        {label}
      </Label>
      <Popover open={searchOpen} onOpenChange={setSearchOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={searchOpen}
            className="w-full !h-11 justify-between text-left font-normal"
          >
            {value || placeholder}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <div className="p-2">
            <Input
              placeholder="Search suppliers..."
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              className="h-9"
            />
          </div>
          <div className="max-h-60 overflow-auto">
            {isLoadingSuppliers ? (
              <div className="p-2 text-sm text-gray-500">
                Loading suppliers...
              </div>
            ) : filteredSuppliers.length === 0 ? (
              <div className="p-2">
                <div className="text-sm text-gray-500 mb-2">
                  No suppliers found.
                </div>
                <button
                  onClick={handleAddSupplier}
                  className="w-full flex items-center justify-center gap-2 px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-md border border-blue-200 hover:border-blue-300 transition-colors"
                >
                  <Plus className="h-4 w-4" />
                  Add New Supplier
                </button>
              </div>
            ) : (
              filteredSuppliers.map((supplier: Supplier) => (
                <button
                  key={supplier.id}
                  onClick={() => handleSupplierSelect(supplier.name)}
                  className={cn(
                    "w-full px-2 py-2 text-left text-sm hover:bg-gray-100 flex items-center",
                    value === supplier.name && "bg-gray-100"
                  )}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === supplier.name ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {supplier.name}
                </button>
              ))
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default SupplierSearchDropdown;
