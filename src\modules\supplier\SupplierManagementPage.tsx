import { useState } from "react";
import toast from "react-hot-toast";
import SupplierHeader from "./components/SupplierHeader";
import { SupplierTable } from "./components/supplierTable/SupplierTable";
import { useSuppliers } from "./hooks/use-suppliers";
import type { Supplier } from "@/types/supplier";
import AddOrEditSupplierDrawer from "./components/AddOrEditSupplierDrawer";
import { SupplierDeleteModal } from "./components/SupplierDeleteModal";
import { deleteSupplier } from "@/services/api";

const SupplierManagementPage = () => {
  const { data, isLoading, error, refetch } = useSuppliers();
  const [searchTerm, setSearchTerm] = useState("");
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState<Supplier | null>(null);

  const handleEdit = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setIsDrawerOpen(true);
  };

  const handleDelete = (supplier: Supplier) => {
    setSelectedSupplier(supplier);
    setIsModalOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedSupplier) {
      try {
        await deleteSupplier(selectedSupplier.id);
        toast.success("Supplier deleted successfully");
        refetch();
      } catch (error) {
        toast.error("Failed to delete supplier");
      } finally {
        setIsModalOpen(false);
        setSelectedSupplier(null);
      }
    }
  };

  const filteredData = data?.filter((supplier: Supplier) =>
    supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    supplier.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6 p-10 bg-[#FCFCFC] h-[90vh]">
      <SupplierHeader searchTerm={searchTerm} onSearch={setSearchTerm} onSupplierUpdate={refetch} />
      <SupplierTable
        data={filteredData}
        isLoading={isLoading}
        error={error as Error | null}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
      <AddOrEditSupplierDrawer
        open={isDrawerOpen}
        onOpenChange={setIsDrawerOpen}
        supplier={selectedSupplier}
        onSupplierUpdate={() => {
          refetch();
          setIsDrawerOpen(false);
        }}
      />
      <SupplierDeleteModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onConfirm={handleConfirmDelete}
        supplier={selectedSupplier}
      />
    </div>
  );
};

export default SupplierManagementPage;
