import { useState, useEffect, useCallback, useRef } from "react";
import { DataTable } from "@/components/DataTable";
import { projectTableColumns } from "./column";
import { getProjects, deleteProject } from "@/services/api";
import type { Project } from "@/types/project";
import DeleteProjectModal from "./DeleteProjectModal";

interface ProjectTableProps {
  searchTerm: string;
  refreshTrigger?: number; // Optional prop to trigger refresh from parent
  onProjectsChange?: (projects: Project[]) => void; // Callback to pass projects back to parent
}

const ProjectTable = ({ 
  searchTerm, 
  refreshTrigger = 0,
  onProjectsChange 
}: ProjectTableProps) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [projectToDelete, setProjectToDelete] = useState<Project | null>(null);
  
  // Use ref to avoid recreating fetchProjects on every render
  const onProjectsChangeRef = useRef(onProjectsChange);
  onProjectsChangeRef.current = onProjectsChange;

  // Helper function to determine project status based on dates
  const determineProjectStatus = (
    startDate: string,
    dueDate: string
  ): "ACTIVE" | "UPCOMING" | "PAST" => {
    const now = new Date();
    const start = new Date(startDate);
    const due = new Date(dueDate);

    if (now < start) return "UPCOMING";
    if (now > due) return "PAST";
    return "ACTIVE";
  };

  // Fetch projects from API
  const fetchProjects = useCallback(async () => {
    try {
      setLoading(true);
      const data = await getProjects();
      // Add status based on dates for demo purposes
      const projectsWithStatus = data.map((project: Project) => ({
        ...project,
        status: determineProjectStatus(project.startDate, project.dueDate),
      }));
      setProjects(projectsWithStatus);
      
      // Pass projects back to parent if callback is provided
      if (onProjectsChangeRef.current) {
        onProjectsChangeRef.current(projectsWithStatus);
      }
    } catch (err) {
      setError("Failed to fetch projects");
      console.error("Error fetching projects:", err);
    } finally {
      setLoading(false);
    }
  }, []); // Remove onProjectsChange from dependencies to prevent infinite loop

  useEffect(() => {
    fetchProjects();
  }, [refreshTrigger, fetchProjects]); // Re-fetch when refreshTrigger changes

  // Handle delete project
  const handleDeleteProject = (project: Project) => {
    setProjectToDelete(project);
    setDeleteModalOpen(true);
  };

  const handleConfirmDelete = async (project: Project) => {
    try {
      // Call the delete API
      await deleteProject(project.id);
      
      // Remove project from local state
      const updatedProjects = projects.filter(p => p.id !== project.id);
      setProjects(updatedProjects);
      
      // Pass updated projects back to parent if callback is provided
      if (onProjectsChangeRef.current) {
        onProjectsChangeRef.current(updatedProjects);
      }
      
      setDeleteModalOpen(false);
      setProjectToDelete(null);
    } catch (err) {
      console.error('Error deleting project:', err);
      setError('Failed to delete project');
    }
  };

  const filteredProjects = projects.filter(
    (project) =>
      project.projectName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.projectCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="mt-6 flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading projects...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-6 flex items-center justify-center py-8">
        <div className="text-center">
          <p className="text-red-500">{error}</p>
          <button
            onClick={fetchProjects}
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-6">
      <DataTable 
        columns={projectTableColumns} 
        data={filteredProjects || []} 
        meta={{
          onDelete: handleDeleteProject
        }}
      />
      
      <DeleteProjectModal
        open={deleteModalOpen}
        onOpenChange={setDeleteModalOpen}
        project={projectToDelete}
        onConfirm={handleConfirmDelete}
      />
    </div>
  );
};

export default ProjectTable;