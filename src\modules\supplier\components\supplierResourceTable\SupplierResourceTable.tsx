import { DataTable } from "@/components/DataTable";
import { columns } from "./columns";
import type { SupplierResource } from "@/types/supplier";

interface SupplierResourceTableProps {
  data: SupplierResource[];
  isLoading: boolean;
  error: Error | null;
}

const SupplierResourceTable = ({ data, isLoading, error }: SupplierResourceTableProps) => {
  console.log(error);
  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error loading suppliers</div>;
  }

  return (
    <div className="mt-6">
      <DataTable columns={columns} data={data || []} />
    </div>
  );
};

export default SupplierResourceTable;
