"use client"

import { DataTable } from "@/components/DataTable"
import { columns } from "./column"
import { LoadingState, ErrorState } from "@/components/common/PageStates"
import type { Supplier } from "@/types/supplier"

interface SupplierTableProps {
  data: Supplier[] | undefined
  isLoading: boolean
  error: Error | null
  onEdit: (supplier: Supplier) => void
  onDelete: (supplier: Supplier) => void
}

export function SupplierTable({ data, isLoading, error, onEdit, onDelete }: SupplierTableProps) {
  if (isLoading) {
    return <LoadingState />
  }

  if (error) {
    return <ErrorState error={error} />
  }

  return (
    <div>
      <DataTable columns={columns} data={data || []} meta={{ onEdit, onDelete }} />
    </div>
  )
}
