import React, { useState, useEffect } from 'react';
import { getResourceInventory } from '@/services/api';

interface Receipt {
  id: string;
  supplierResourceCode: string;
  availableQuantity: number;
  pricePerUnit: string;
  price: string;
  addedQuantity: number;
  addedOn: string;
  unitType: string;
  location: string;
  supplier: {
    id: string;
    name: string;
    code: string;
    contactEmail: string;
    phoneNumber: string;
    address: string;
    contactPerson: string;
  };
}

interface RecentReceiptProps {
  resourceId: string;
}

const RecentReceipt: React.FC<RecentReceiptProps> = ({ resourceId }) => {
  const [receiptHistory, setReceiptHistory] = useState<Receipt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchReceipts = async () => {
      try {
        setLoading(true);
        const data = await getResourceInventory(resourceId);
        setReceiptHistory(data);
        setError(null);
      } catch (err) {
        setError('Failed to fetch receipt history.');
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    if (resourceId) {
      fetchReceipts();
    }
  }, [resourceId]);

  if (loading) {
    return <div>Loading receipts...</div>;
  }

  if (error) {
    return <div>{error}</div>;
  }

  if (!receiptHistory || receiptHistory.length === 0) {
    return null; // Or some placeholder
  }

  const latestReceipt = receiptHistory[0];
  console.log(latestReceipt);

  return (
    <div>
      <h3 className="text-lg font-medium text-gray-900">Recent Receipt</h3>
      <div className="bg-primary-50 rounded-lg p-6 mb-6">
        <div className="flex items-center justify-between mb-2">
          <h4 className="font-medium text-sm text-gray-900">
            #{latestReceipt.id || "000"}
          </h4>
          <span className="text-green-600 font-medium text-sm">
            +{latestReceipt.addedQuantity || 0} Units
          </span>
        </div>
        {latestReceipt.addedOn && (
          <p className="text-xs text-gray-500 mb-4">{new Date(latestReceipt.addedOn).toLocaleDateString()}</p>
        )}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Supplier:</span>
            <span className="text-gray-900">{latestReceipt.supplier.name}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Resource Code:</span>
            <span className="text-gray-900">{latestReceipt.supplierResourceCode}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Unit Price:</span>
            <span className="text-gray-900">
              ${parseFloat(latestReceipt.pricePerUnit)?.toFixed(2) || "0.00"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecentReceipt;
