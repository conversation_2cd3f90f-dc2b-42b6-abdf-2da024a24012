import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Info, Copy, Check } from 'lucide-react'
import type { User } from '@/types/user'
import { resetUserPassword } from '@/services/api'

interface ResetPasswordModalProps {
  isOpen: boolean
  onClose: () => void
  user: User | null
}

interface ResetPasswordResponse {
  temporaryPassword: string
  employeeId: string
  userName: string
}

const ResetPasswordModal: React.FC<ResetPasswordModalProps> = ({
  isOpen,
  onClose,
  user
}) => {
  const [isLoading, setIsLoading] = useState(false)
  const [resetData, setResetData] = useState<ResetPasswordResponse | null>(null)
  const [copied, setCopied] = useState(false)

  if (!user) return null

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "supervisor":
        return "bg-red-100 text-red-800 border-red-200";
      case "mechanic":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getUserInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  const handleResetPassword = async () => {
    setIsLoading(true)
    try {
      // Make real API call to reset password
      const response = await resetUserPassword(user.id)
      
      // Check if the response was successful
      if (response.success) {
        // Transform response to match our interface
        const resetData: ResetPasswordResponse = {
          temporaryPassword: response.temporaryPassword,
          employeeId: user.employeeId,
          userName: `${user.firstName} ${user.lastName}`
        }
        
        setResetData(resetData)
        // Modal will stay open to show the new password
      } else {
        throw new Error(response.message || 'Failed to reset password')
      }
    } catch (error) {
      console.error('Failed to reset password:', error)
      // For now, show mock data if API fails (for development/testing)
      const mockResponse: ResetPasswordResponse = {
        temporaryPassword: '4gtsc@6787c',
        employeeId: user.employeeId,
        userName: `${user.firstName} ${user.lastName}`
      }
      setResetData(mockResponse)
      // Modal will stay open to show the mock password
    } finally {
      setIsLoading(false)
    }
  }

  const handleCopyPassword = async () => {
    if (resetData?.temporaryPassword) {
      try {
        await navigator.clipboard.writeText(resetData.temporaryPassword)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000) // Reset after 2 seconds
      } catch (error) {
        console.error('Failed to copy to clipboard:', error)
      }
    }
  }

  const handleClose = () => {
    setResetData(null)
    setCopied(false)
    onClose()
  }

  // Success state - show generated password
  if (resetData) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <div className="text-center py-6">
            <DialogTitle className="text-xl font-semibold text-gray-900 mb-4">
              Password reset successful..!
            </DialogTitle>
            
            <p className="text-sm text-gray-600 mb-6">
              A temporary password has been generated for {resetData.userName} ({resetData.employeeId})
            </p>
            
            {/* Password Display */}
            <div className="bg-blue-50 rounded-lg p-4 mb-6">
              <p className="text-sm text-gray-600 mb-2">New password</p>
              <div className="flex items-center justify-between bg-white rounded border p-3">
                <span className="font-mono text-lg font-semibold text-gray-900">
                  {resetData.temporaryPassword}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopyPassword}
                  className="ml-2 text-gray-600 hover:text-gray-900"
                >
                  {copied ? (
                    <>
                      <Check className="h-4 w-4 mr-1" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </>
                  )}
                </Button>
              </div>
            </div>
            
            {/* Confirm Button */}
            <Button
              onClick={handleClose}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
            >
              Confirm
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  // Confirmation state - show user info and confirm action
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-100">
            <Info className="h-8 w-8 text-blue-600" />
          </div>
          <DialogTitle className="text-xl font-semibold text-gray-900">
            Reset password
          </DialogTitle>
        </DialogHeader>
        
        <div className="text-center">
          <p className="text-sm text-gray-600 mb-6">
            Are you sure you want to reset password for this user? Once the password is reset, user cannot login with old password.
          </p>
          
          {/* User Info Card */}
          <div className="flex items-center justify-between bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                  {getUserInitials(user.firstName, user.lastName)}
                </AvatarFallback>
              </Avatar>
              <div className="text-left">
                <p className="font-medium text-gray-900">
                  {user.firstName} {user.lastName}
                </p>
              </div>
            </div>
            <Badge
              className={`${getRoleBadgeColor(user.role)} rounded px-3 py-1 text-xs font-medium border-0`}
              variant="outline"
            >
              {user.role.toUpperCase()}
            </Badge>
          </div>
          
          {/* Action Buttons */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleResetPassword}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              disabled={isLoading}
            >
              {isLoading ? 'Resetting...' : 'Reset'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default ResetPasswordModal