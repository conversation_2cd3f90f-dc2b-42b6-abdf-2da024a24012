import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import type { Project } from "@/types/project";
import ProjectTableToolbar from "./components/ProjectTableToolbar";
import ProjectsSection from "./components/ProjectsSection";
import NewProjectDrawer from "./components/NewProjectDrawer";

const ProjectManagementPage = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [projects, setProjects] = useState<Project[]>([]);
  const [isNewProjectDrawerOpen, setIsNewProjectDrawerOpen] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Callback to receive projects from ProjectTable
  const handleProjectsChange = (newProjects: Project[]) => {
    setProjects(newProjects);
  };

  // Function to trigger refresh (e.g., after creating a new project)
  const refreshProjects = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <div className="p-6 space-y-6 bg-[#FCFCFC] min-h-[92vh]">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold tracking-tight">
            Manage your projects
          </h1>
          <p className="text-muted-foreground">
            Track, organize, and stay in control of all your projects.
          </p>
        </div>
        <Button 
          className="bg-blue-500 hover:bg-blue-600 text-white"
          onClick={() => setIsNewProjectDrawerOpen(true)}
        >
          <Plus className="h-4 w-4 mr-2" />
          Start New Project
        </Button>
      </div>

      {/* Search */}
      <ProjectTableToolbar
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
      />

      {/* Projects Section */}
      <ProjectsSection
        searchTerm={searchTerm}
        projects={projects}
        refreshTrigger={refreshTrigger}
        onProjectsChange={handleProjectsChange}
      />

      {/* New Project Drawer */}
      <NewProjectDrawer
        open={isNewProjectDrawerOpen}
        onOpenChange={setIsNewProjectDrawerOpen}
        onProjectCreated={refreshProjects}
      />
    </div>
  );
};

export default ProjectManagementPage;
