import { useState } from "react";
import Pick from "./components/Pick";
import Drop from "./components/Drop";
import InventoryHeader from "./components/InventoryHeader";

const InventoryManagementPage = () => {
  const [isPick, setIsPick] = useState(true);
  const [selectedUserId, setSelectedUserId] = useState<string>();
  const [selectedProjectId, setSelectedProjectId] = useState<string>();

  return (
    <div className="flex flex-col h-[92vh] border overflow-clip">
      <InventoryHeader 
        isPick={isPick} 
        setIsPick={setIsPick}
        selectedUserId={selectedUserId}
        selectedProjectId={selectedProjectId}
        onUserChange={setSelectedUserId}
        onProjectChange={setSelectedProjectId}
      />
      {isPick ? (
        <Pick userId={selectedUserId} projectId={selectedProjectId} />
      ) : (
        <Drop userId={selectedUserId} />
      )}
    </div>
  );
};

export default InventoryManagementPage;
