import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  She<PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>eader, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  She<PERSON><PERSON>ooter 
} from '@/components/ui/sheet'
import { Eye, EyeOff, X } from 'lucide-react'
import type { UserFormData, User } from '@/types/user'
import { z } from 'zod'
import toast from 'react-hot-toast'

const userFormSchema = z.object({
  employeeId: z.string().min(1, 'Employee ID is required'),
  email: z.string().email('Invalid email address'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  password: z.string().optional(),
  roles: z.array(z.string()).min(1, 'At least one role must be selected'),
  isActive: z.boolean().optional()
})

interface AddOrEditUserDrawerProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (userData: UserFormData) => void
  user?: User | null // User to edit (null/undefined for add mode)
}

const AddOrEditUserDrawer = ({ isOpen, onClose, onSubmit, user }: AddOrEditUserDrawerProps) => {
  const isEditMode = !!user

  const [formData, setFormData] = useState<UserFormData>({
    employeeId: '',
    email: '',
    firstName: '',
    lastName: '',
    password: '',
    roles: [],
    isActive: true
  })
  
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  // Populate form data when editing
  useEffect(() => {
    if (user && isEditMode) {
      setFormData({
        employeeId: user?.employeeId || '',
        email: user?.email || '',
        firstName: user?.firstName || '',
        lastName: user?.lastName || '',
        password: '', // Don't populate password for security
        roles: user?.roles || [user?.role || 'mechanic'],
        isActive: user?.isActive !== undefined ? user.isActive : true
      })
    } else {
      // Reset to default values for add mode
      setFormData({
        employeeId: '',
        email: '',
        firstName: '',
        lastName: '',
        password: '',
        roles: [],
        isActive: true
      })
    }
    setConfirmPassword('')
  }, [user, isEditMode, isOpen])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate password confirmation (only for new users or when password is being changed)
    if (!isEditMode || formData.password) {
      if (formData.password !== confirmPassword) {
        toast.error('Passwords do not match')
        return
      }
    }
    
    try {
      // Exclude 'role' from the data sent to the API
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { role, ...dataToSubmit } = formData
      
      // Validate the form data
      const validationResult = userFormSchema.safeParse(dataToSubmit)
      if (!validationResult.success) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const errors = validationResult.error.issues.map((err: any) => err.message).join('\n')
        toast.error(`Validation errors:\n${errors}`)
        return
      }
      
      // Additional password validation for add mode
      if (!isEditMode && !dataToSubmit.password) {
        toast.error('Password is required for new users')
        return
      }
      
      await onSubmit(dataToSubmit)
      onClose()
      // Reset form
      setFormData({
        employeeId: '',
        email: '',
        firstName: '',
        lastName: '',
        password: '',
        roles: [],
        isActive: true
      })
      setConfirmPassword('')
    } catch (error) {
      console.error('Failed to save user:', error)
      toast.error('Failed to save user. Please try again.')
    }
  }

  const handleRoleChange = (role: string) => {
    setFormData(prev => ({
      ...prev,
      roles: [role]
    }))
  }

  if (!isOpen) return null

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md">
        <SheetHeader className="pb-6">
          <div className="flex items-center justify-between">
            <SheetTitle className="text-xl font-semibold text-gray-900">
              {isEditMode ? 'Edit User' : 'Add New User'}
            </SheetTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="p-2 hover:bg-gray-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
          <p className="text-sm text-gray-500 mt-2">
            {isEditMode 
              ? 'Update the user\'s information and settings.' 
              : 'Enter the user\'s basic details, assign their role, and activate their account.'
            }
          </p>
        </SheetHeader>

        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          <div className="flex-1 space-y-6 px-6">
            {/* Role Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium text-gray-700">SELECT ROLE</Label>
              <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                {['mechanic', 'supervisor', 'admin'].map((role) => (
                  <button
                    key={role}
                    type="button"
                    onClick={() => handleRoleChange(role)}
                    className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                      formData.roles[0] === role
                        ? 'bg-blue-500 text-white shadow-sm'
                        : 'text-gray-600 hover:text-gray-800'
                    }`}
                  >
                    {role.charAt(0).toUpperCase() + role.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* Emirates ID */}
            <div className="space-y-2">
              <Label htmlFor="employeeId" className="text-sm font-medium text-gray-700">
                Emirates ID
              </Label>
              <Input
                id="employeeId"
                type="text"
                value={formData.employeeId}
                onChange={(e) => setFormData(prev => ({ ...prev, employeeId: e.target.value }))}
                placeholder="Enter user's emirates ID"
                required
                className="w-full"
              />
            </div>

            {/* Email Address */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                placeholder="Enter user's email address"
                required
                className="w-full"
              />
            </div>

            {/* First Name */}
            <div className="space-y-2">
              <Label htmlFor="firstName" className="text-sm font-medium text-gray-700">
                First Name
              </Label>
              <Input
                id="firstName"
                type="text"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                placeholder="Enter user's first name"
                required
                className="w-full"
              />
            </div>

            {/* Last Name */}
            <div className="space-y-2">
              <Label htmlFor="lastName" className="text-sm font-medium text-gray-700">
                Last Name
              </Label>
              <Input
                id="lastName"
                type="text"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                placeholder="Enter user's first name"
                required
                className="w-full"
              />
            </div>

            {/* Password */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                Password {isEditMode && <span className="text-gray-400">(leave blank to keep current)</span>}
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                  placeholder={isEditMode ? "Enter new password (optional)" : "Enter temporary password"}
                  required={!isEditMode}
                  className="w-full pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
            </div>

            {/* Confirm Password */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-700">
                Confirm Password
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  placeholder={isEditMode ? "Confirm new password" : "Confirm temporary password"}
                  required={!isEditMode ? true : !!formData.password}
                  className="w-full pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
            </div>
          </div>

          <SheetFooter className="flex flex-row space-x-3 pt-6 px-6">
            <Button 
              type="button" 
              variant="outline" 
              onClick={onClose} 
              className="flex-1 border-gray-300"
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              className="flex-1 bg-blue-500 hover:bg-blue-600 text-white"
            >
              {isEditMode ? 'Update User' : 'Add User'}
            </Button>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  )
}

export default AddOrEditUserDrawer
