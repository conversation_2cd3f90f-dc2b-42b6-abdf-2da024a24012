import { Wrench, Package, Users, DollarSign } from 'lucide-react'

interface ProjectStatsProps {
  totalAssets: number
  totalConsumables: number
  totalResources: number
  totalProjectCost: number
}

const ProjectStats = ({ 
  totalAssets, 
  totalConsumables, 
  totalResources, 
  totalProjectCost 
}: ProjectStatsProps) => {
  return (
    <div className="grid grid-cols-4 gap-6 p-6 bg-white rounded-lg border h-44">
      {/* Total Assets */}
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
          <Wrench className="h-6 w-6 text-blue-500" />
        </div>
        <div>
          <p className="text-sm text-gray-500">Total Assets</p>
          <p className="text-2xl font-bold text-gray-900">{totalAssets}</p>
        </div>
      </div>

      {/* Total Consumables */}
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
          <Package className="h-6 w-6 text-orange-500" />
        </div>
        <div>
          <p className="text-sm text-gray-500">Total Consumables</p>
          <p className="text-2xl font-bold text-gray-900">{totalConsumables}</p>
        </div>
      </div>

      {/* Total Resources */}
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
          <Users className="h-6 w-6 text-red-500" />
        </div>
        <div>
          <p className="text-sm text-gray-500">Total Resources</p>
          <p className="text-2xl font-bold text-gray-900">{totalResources}</p>
        </div>
      </div>

      {/* Total Project Cost */}
      <div className="flex items-center gap-4">
        <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
          <DollarSign className="h-6 w-6 text-green-500" />
        </div>
        <div>
          <p className="text-sm text-gray-500">Total Project Cost</p>
          <p className="text-2xl font-bold text-gray-900">${totalProjectCost.toLocaleString()}</p>
        </div>
      </div>
    </div>
  )
}

export default ProjectStats
