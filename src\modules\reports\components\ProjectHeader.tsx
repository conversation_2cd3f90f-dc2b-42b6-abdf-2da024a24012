import { ArrowLeft, Calendar } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import ProjectStats from "./ProjectStats";

interface ProjectHeaderProps {
  projectName: string;
  projectCode: string;
  status: "ACTIVE" | "PAST" | "UPCOMING";
  startDate: string;
  dueDate: string;
  onBack: () => void;
  totalAssets: number;
  totalConsumables: number;
  totalResources: number;
  totalProjectCost: number;
}

const ProjectHeader = ({
  projectName,
  projectCode,
  status,
  startDate,
  dueDate,
  onBack,
  totalAssets,
  totalConsumables,
  totalResources,
  totalProjectCost,
}: ProjectHeaderProps) => {
  return (
    <div className="bg-white border-b">
      <div className="p-6">
        <div className="flex items-start justify-between mb-4 p-4">
          <Button
            variant="ghost"
            onClick={onBack}
            className="flex items-center gap-2 p-0 h-auto font-normal text-gray-600"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        </div>

        <div className="flex  mb-6 gap-5 h-44">
          <div className="w-[30%] flex flex-col gap-4 border items-center justify-center rounded-lg ">
            <div className="flex items-center gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {projectName}
                </h1>
                <p className="text-gray-500">{projectCode}</p>
              </div>
              <Badge className="bg-green-500 text-white border-transparent hover:bg-green-600">
                {status}
              </Badge>
            </div>

            <div className="flex gap-8">
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-500">Start Date</p>
                  <p className="font-medium">{startDate}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-500" />
                <div>
                  <p className="text-sm text-gray-500">Due date</p>
                  <p className="font-medium">{dueDate}</p>
                </div>
              </div>
            </div>
          </div>
          <div className="w-[70%] ml-auto pr-6 pb-6  ">
            <ProjectStats
              totalAssets={totalAssets}
              totalConsumables={totalConsumables}
              totalResources={totalResources}
              totalProjectCost={totalProjectCost}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectHeader;
