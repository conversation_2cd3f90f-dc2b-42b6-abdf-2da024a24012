export interface User {
  id: string
  employeeId: string
  email: string
  firstName: string
  lastName: string
  role: string
  roles: string[]
  isActive: boolean
  isFirstLogin: boolean
  createdAt: string
  updatedAt: string
}

export interface UserFormData {
  employeeId: string
  email: string
  firstName: string
  lastName: string
  password: string
  role?: string
  roles: string[]
  isActive?: boolean
}

export type UserRole = 'admin' | 'supervisor' | 'mechanic'

export type UserStatus = boolean // true for active, false for inactive

export interface UserFilters {
  searchTerm: string
  role: string
  status: string
}

export interface UserTableProps {
  users: User[]
  onEdit: (user: User) => void
  onDelete: (userId: string) => void
  onToggleStatus: (userId: string) => void
  onResetPassword: (userId: string) => void
}
