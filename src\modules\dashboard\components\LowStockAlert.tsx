import { ChevronRight } from "lucide-react";

interface LowStockItem {
  id: string;
  name: string;
  code: string;
  available: number;
  minQty: number;
  image: string;
}

const LowStockAlert = () => {
  const lowStockItems: LowStockItem[] = [
    {
      id: "1",
      name: "Cutting disc 125mmX1.0X22mm",
      code: "ABR0003",
      available: 23,
      minQty: 32,
      image: "/api/placeholder/40/40"
    },
    {
      id: "2",
      name: "Abrasiflex 125×22mm 280 fl...",
      code: "ABR0005",
      available: 4,
      minQty: 15,
      image: "/api/placeholder/40/40"
    },
    {
      id: "3",
      name: "Klingsport125×6×22 grinding",
      code: "ABR0003",
      available: 0,
      minQty: 0,
      image: "/api/placeholder/40/40"
    }
  ];

  return (
    <div className="bg-white rounded-lg border mb-8">
      <div className="flex items-center justify-between p-6 border-b">
        <div className="flex items-center gap-3">
          <div className="bg-red-100 text-red-600 px-3 py-1 rounded-full text-sm font-medium">
            Low stock Alert
          </div>
          <div className="bg-red-500 text-white px-3 py-1 rounded text-sm font-medium">
            3 ITEMS
          </div>
        </div>
        <button className="flex items-center gap-1 text-blue-500 text-sm font-medium hover:text-blue-600">
          View All
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>

      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {lowStockItems.map((item) => (
            <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
              <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                <div className="w-8 h-8 bg-gray-400 rounded"></div>
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-900 text-sm">{item.name}</h4>
                <p className="text-xs text-gray-600 mb-1">{item.code}</p>
                <div className="text-xs">
                  <span className="text-gray-600">Available:</span>
                  <span className="font-medium text-gray-900 ml-1">{item.available}</span>
                </div>
                <div className="text-xs">
                  <span className="text-gray-600">Min Qty:</span>
                  <span className="font-medium text-gray-900 ml-1">{item.minQty}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default LowStockAlert;
