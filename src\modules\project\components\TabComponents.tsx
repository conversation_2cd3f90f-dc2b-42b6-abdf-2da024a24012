import React, { useRef, useEffect, useState } from "react";
import { cn } from "@/lib/utils";

// Tab component since it doesn't exist in the UI library
interface TabsProps {
  value: string;
  onValueChange: (value: string) => void;
  children: React.ReactNode;
  className?: string;
}

export const Tabs: React.FC<TabsProps> = ({
  value,
  onValueChange,
  children,
  className,
}) => {
  return (
    <div className={cn("w-full", className)}>
      {React.Children.map(children, (child) =>
        React.isValidElement(child)
          ? React.cloneElement(child as React.ReactElement<any>, {
              value,
              onValueChange,
            })
          : child
      )}
    </div>
  );
};

interface TabsListProps {
  children: React.ReactNode;
  className?: string;
  value?: string;
  onValueChange?: (value: string) => void;
}

export const TabsList: React.FC<TabsListProps> = ({ children, className, value }) => {
  const [indicatorStyle, setIndicatorStyle] = useState({ left: 0, width: 0 });
  const listRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (listRef.current && value) {
      const activeButton = listRef.current.querySelector(`[data-value="${value}"]`) as HTMLButtonElement;
      if (activeButton) {
        const listRect = listRef.current.getBoundingClientRect();
        const buttonRect = activeButton.getBoundingClientRect();
        
        setIndicatorStyle({
          left: buttonRect.left - listRect.left,
          width: buttonRect.width,
        });
      }
    }
  }, [value]);

  return (
    <div
      ref={listRef}
      className={cn(
        "relative inline-flex gap-4 h-10 items-center justify-center rounded-lg bg-gray-100 p-1 text-muted-foreground",
        className
      )}
    >
      {/* Sliding indicator */}
      <div
        className="absolute top-1 bottom-1 bg-blue-500 rounded-md transition-all duration-200 ease-in-out"
        style={{
          left: `${indicatorStyle.left}px`,
          width: `${indicatorStyle.width}px`,
        }}
      />
      {children}
    </div>
  );
};

interface TabsTriggerProps {
  value: string;
  children: React.ReactNode;
  className?: string;
  parentValue?: string;
  onValueChange?: (value: string) => void;
}

export const TabsTrigger: React.FC<TabsTriggerProps> = ({
  value,
  children,
  className,
  parentValue,
  onValueChange,
}) => {
  const isActive = parentValue === value;

  return (
    <button
      data-value={value}
      className={cn(
        "relative inline-flex items-center justify-center whitespace-nowrap rounded-md px-10 py-1.5 text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 z-10",
        isActive
          ? "text-white"
          : "text-gray-600 hover:text-gray-900",
        className
      )}
      onClick={() => onValueChange?.(value)}
    >
      {children}
    </button>
  );
};
