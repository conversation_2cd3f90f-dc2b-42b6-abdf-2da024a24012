import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Search, X } from "lucide-react";
import ItemCard from "./ItemCard";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useEffect, useState, useMemo } from "react";
import { getInventoryItems } from "@/services/api";
import type { CheckoutItem } from "./Pick";
import type { Transaction } from "@/types/inventory";

// Updated interface to match the new API response
interface InventoryItem {
    id: string;
    name: string;
    resourceCode: string;
    unitType: string;
    unitSize: number;
    imageUrl: string;
    resourceCategory: string;
    resourceType: string;
    totalAvailableQuantity: number;
    averagePricePerUnit: number;
    minInventoryQty: number;
    maxInventoryQty: number;
}

interface AvailableItemsForPickProps {
    onAddToCheckout: (item: InventoryItem | Transaction) => void;
    checkoutItems: CheckoutItem[];
    onRemoveFromCheckout: (id: string) => void;
    refreshTrigger?: number;
}

const AvailableItemsForPick = ({ onAddToCheckout, checkoutItems, onRemoveFromCheckout, refreshTrigger }: AvailableItemsForPickProps) => {
    const [items, setItems] = useState<InventoryItem[]>([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [itemType, setItemType] = useState("all");
    const [error, setError] = useState<string | null>(null);

    const fetchItems = async () => {
        try {
            const data: InventoryItem[] = await getInventoryItems();
            setItems(data);
        } catch (error) {
            console.error("Failed to fetch items", error);
            setError("Failed to load items.");
        }
    };

    useEffect(() => {
        fetchItems();
    }, []);

    // Refetch items when refreshTrigger changes
    useEffect(() => {
        if (refreshTrigger && refreshTrigger > 0) {
            fetchItems();
        }
    }, [refreshTrigger]);

    const filteredItems = useMemo(() => {
        let filtered = items;

        if (searchTerm) {
            filtered = filtered.filter((item) =>
                item.name.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (itemType !== "all") {
            filtered = filtered.filter((item) => item.resourceType.toLowerCase() === itemType);
        }

        return filtered;
    }, [searchTerm, itemType, items]);

    return (
        <div className="w-full ">
            {error && <div className="text-red-500 mb-4">{error}</div>}
            <div className="flex flex-col justify-between  mb-4">
                <div className="flex items-center gap-4">
                    <h2 className="text-2xl font-bold">Available Items</h2>
                    <Badge variant="secondary" className="bg-success-50 text-success-500 border-success-200 rounded-full">{filteredItems.length} Items</Badge>
                </div>
                <div className="flex items-center justify-between gap-4 mt-6 ">
                    <div className="relative w-64">
                        <Input
                            placeholder="Search Items..."
                            className="pr-10"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        {searchTerm ? (
                            <Button
                                variant="ghost"
                                size="icon"
                                className="absolute right-8 top-1/2 -translate-y-1/2 w-5 h-5"
                                onClick={() => setSearchTerm("")}
                            >
                                <X className="w-4 h-4" />
                            </Button>
                        ) : (
                            <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
                        )}
                    </div>
                    <Select onValueChange={setItemType} value={itemType}>
                        <SelectTrigger className="w-[180px]">
                            <SelectValue placeholder="All Type" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Type</SelectItem>
                            <SelectItem value="consumable">Consumable</SelectItem>
                            <SelectItem value="asset">Asset</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </div>
            <div className="space-y-4 overflow-y-scroll h-[65vh]">
                {filteredItems.map((item) => {
                    const isInCheckout = checkoutItems.some(checkoutItem => checkoutItem.id === item.id);
                    return (
                        <ItemCard
                            key={item.id}
                            item={item}
                            onAddToCheckout={onAddToCheckout}
                            onRemove={() => onRemoveFromCheckout(item.id)}
                            isPicked={isInCheckout}
                        />
                    );
                })}
            </div>
        </div>
    );
};

export default AvailableItemsForPick;
