import { useState } from "react";
import LocationDropdown from "./LocationDropdown";
import RecentReceipt from "./RecentReceipt";
import SupplierSearchDropdown from "./SupplierSearchDropdown";
import { Package, X, Calendar } from "lucide-react";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { addStock } from "@/services/api";
import type {
  InventoryData,
  Resource,
  StockUpdateData,
} from "@/types/resource";

interface UpdateStockDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  resource: Resource | null;
}

const UpdateStockDrawer = ({
  open,
  onOpenChange,
  resource,
}: UpdateStockDrawerProps) => {
  const [formData, setFormData] = useState<StockUpdateData>({
    date: new Date().toISOString().split("T")[0], // Today's date
    location: "",
    supplierName: "",
    supplierCode: "",
    supplierId: "",
    supplierResourceCode: "",
    unitPrice: 0,
    quantity: 0,
  });

  const handleInputChange = (
    field: keyof StockUpdateData,
    value: string | number
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSupplierChange = (
    supplierName: string,
    supplierCode: string,
    supplierId: string
  ) => {
    setFormData((prev) => ({
      ...prev,
      supplierName,
      supplierCode,
      supplierId,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (resource) {
      const inventoryData: InventoryData = {
        supplierResourceCode: formData.supplierResourceCode,
        resourceId: resource.id,
        supplierId: formData.supplierId, // Using actual supplierId now
        quantity: formData.quantity,
        pricePerUnit: formData.unitPrice,
        unitType: resource.unitType,
        userId: "56fe36a4-47cb-46f1-9376-826b2f860571", // Hardcoded for now
        location: formData.location,
        addedOn: new Date(formData.date).toISOString(),
      };

      try {
        await addStock(resource.id, inventoryData);
        // Optionally, you can show a success message here
      } catch (error) {
        console.error("Failed to add stock:", error);
        // Optionally, you can show an error message here
      }
    }

    // Reset form
    setFormData({
      date: new Date().toISOString().split("T")[0],
      location: "",
      supplierName: "",
      supplierCode: "",
      supplierId: "",
      supplierResourceCode: "",
      unitPrice: 0,
      quantity: 0,
    });
    onOpenChange(false);
  };

  const isFormValid =
    formData.date &&
    formData.location &&
    formData.supplierName &&
    formData.quantity > 0 &&
    formData.unitPrice > 0;

  if (!resource) return null;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="w-full sm:max-w-[60vw] p-10 [&>button]:hidden">
        <div className="flex flex-col h-full">
          {/* Header */}
          <SheetHeader className="p-0">
            {/* Close Button */}
            <Button
              variant="ghost"
              size="icon"
              className="w-fit p-2 bg-gray-100"
              onClick={() => onOpenChange(false)}
            >
              <X className="h-4 w-4" />
            </Button>

            {/* Title with margin */}
            <div className="mt-10">
              <SheetTitle className="text-xl font-semibold text-gray-900">
                Update Stock
              </SheetTitle>
              <SheetDescription className="text-sm text-gray-600 ">
                Restock items to keep your inventory accurate.
              </SheetDescription>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 pb-6 mt-14">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Resource Details & Form */}
              <div className="space-y-6">
                {/* Resource Details */}
                <div className="space-y-2">
                  <h3 className="text-xl font-medium text-gray-900">
                    Resource details
                  </h3>
                  <div className="bg-primary-50 rounded-lg p-4">
                    <div className="flex items-center gap-3">
                      <div className="h-16 w-16 rounded border flex items-center justify-center bg-white">
                        {resource.imageUrl ? (
                          <img
                            src={resource.imageUrl}
                            alt={resource.name}
                            className="h-full w-full object-cover rounded"
                          />
                        ) : (
                          <Package className="h-6 w-6 text-gray-400" />
                        )}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">
                          {resource.name}
                        </h4>
                        <p className="text-sm text-gray-500">
                          Available: {resource.totalAvailableQuantity || 0}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-6 px-1">
                  {/* Date */}
                  <div className="space-y-2">
                    <Label
                      htmlFor="date"
                      className="text-sm font-medium text-gray-700"
                    >
                      Date
                    </Label>
                    <div className="relative">
                      <Input
                        id="date"
                        type="date"
                        value={formData.date}
                        onChange={(e) =>
                          handleInputChange("date", e.target.value)
                        }
                        className="w-full h-11 pr-10 [&::-webkit-calendar-picker-indicator]:opacity-0 [&::-webkit-calendar-picker-indicator]:absolute [&::-webkit-calendar-picker-indicator]:right-0 [&::-webkit-calendar-picker-indicator]:w-10 [&::-webkit-calendar-picker-indicator]:h-full [&::-webkit-calendar-picker-indicator]:cursor-pointer"
                      />
                      <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-primary-500 pointer-events-none" />
                    </div>
                  </div>

                  {/* Location */}
                  <LocationDropdown
                    value={formData.location}
                    onChange={(value: string) =>
                      handleInputChange("location", value)
                    }
                    placeholder="Select location"
                  />

                  {/* Supplier Details */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-semibold text-grey-900 uppercase tracking-wide">
                        SUPPLIER DETAILS
                      </h4>
                    </div>

                    <SupplierSearchDropdown
                      value={formData.supplierName}
                      onChange={handleSupplierChange}
                      placeholder="Select Supplier"
                      label="Supplier Name"
                    />

                    <div className="space-y-2">
                      <Label
                        htmlFor="supplierCode"
                        className="text-sm font-medium text-gray-700"
                      >
                        Supplier Code
                      </Label>
                      <Input
                        id="supplierCode"
                        placeholder="Auto filled on selecting supplier"
                        value={formData.supplierCode}
                        readOnly
                        className="w-full h-11 bg-gray-100"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="supplierResourceCode"
                        className="text-sm font-medium text-gray-700"
                      >
                        Supplier resource code
                      </Label>
                      <Input
                        id="supplierResourceCode"
                        placeholder="Enter supplier resource code"
                        value={formData.supplierResourceCode}
                        onChange={(e) =>
                          handleInputChange(
                            "supplierResourceCode",
                            e.target.value
                          )
                        }
                        className="w-full h-11"
                      />
                    </div>
                  </div>
                </form>
              </div>

              {/* Right Column - Unit Price, Quantity, Total Cost and Action Buttons */}
              <div className="space-y-6 flex flex-col justify-between">
                <RecentReceipt resourceId={resource.id} />

                <div className="space-y-4">
                  {/* Unit Price and Quantity */}
                  <div className="space-y-4 ">
                    <div className="space-y-2">
                      <Label
                        htmlFor="unitPrice"
                        className="text-sm font-medium text-gray-700"
                      >
                        Unit Price
                      </Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                          $
                        </span>
                        <Input
                          id="unitPrice"
                          type="number"
                          // step="0.01"
                          min="0"
                          value={formData.unitPrice || ""}
                          onChange={(e) =>
                            handleInputChange(
                              "unitPrice",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          className="w-full pl-8 h-11"
                          placeholder="Enter unit price"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="quantity"
                        className="text-sm font-medium text-gray-700"
                      >
                        Quantity
                      </Label>
                      <Input
                        id="quantity"
                        type="number"
                        min="1"
                        value={formData.quantity || ""}
                        onChange={(e) =>
                          handleInputChange(
                            "quantity",
                            parseInt(e.target.value) || 0
                          )
                        }
                        className="w-full h-11"
                        placeholder="Enter quantity"
                      />
                    </div>
                  </div>

                  {/* Total Cost */}
                  <div className="bg-yellow-100 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <span className="text-lg font-medium text-gray-900">
                        Total Cost
                      </span>
                      <span className="text-xl font-bold text-gray-900">
                        $ {(formData.unitPrice * formData.quantity).toFixed(2)}
                      </span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="pt-4 grid grid-cols-2 gap-4">
                    <Button
                      type="button"
                      variant="outline"
                      className="py-3 px-8"
                      onClick={() => onOpenChange(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      className="bg-cyan-500 hover:bg-cyan-600 text-white py-3 px-8"
                      disabled={!isFormValid}
                      onClick={handleSubmit}
                    >
                      Add stock
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default UpdateStockDrawer;
