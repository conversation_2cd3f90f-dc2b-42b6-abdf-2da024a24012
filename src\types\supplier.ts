export interface Supplier {
  id: string
  name: string
  code: string
  phoneNumber: string
  contactPerson: string
  contactEmail: string
  address: string
}

export interface SupplierFormData {
  name: string
  code: string
  phoneNumber: string
  contactPerson: string
  contactEmail: string
  address: string
}

export interface SupplierResource {
  id: string
  name: string
  code: string
  supplierResourceCode: string
  quantityAvailable: number
  unit: string
  pricePerUnit: number
  category: string
  location: string
  addedOn: string
  image?: string
}

export interface SupplierResourcesResponse {
  supplierId: string
  supplierName: string
  totalResourceCount: number
  resources: SupplierResource[]
}
