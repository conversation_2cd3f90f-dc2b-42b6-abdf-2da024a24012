"use client"

import { type ColumnDef } from "@tanstack/react-table"
import { type User } from "@/types/user"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {  <PERSON><PERSON><PERSON>, <PERSON>ota<PERSON><PERSON><PERSON>w<PERSON><PERSON>, Trash2 } from "lucide-react"

const getRoleBadgeColor = (role: string) => {
  switch (role) {
    case "admin":
      return "bg-blue-100 text-blue-800 border-blue-200";
    case "supervisor":
      return "bg-red-100 text-red-800 border-red-200";
    case "mechanic":
      return "bg-yellow-100 text-yellow-800 border-yellow-200";
    default:
      return "bg-gray-100 text-gray-800 border-gray-200";
  }
};

interface GetColumnsProps {
  resetUserPassword: (userId: string) => void;
  onEditUser: (user: User) => void;
  onDeleteUser: (user: User) => void;
  onToggleUserStatus: (user: User) => void;
}

export const getColumns = ({
  resetUserPassword,
  onEditUser,
  onDeleteUser,
  onToggleUserStatus,
}: GetColumnsProps): ColumnDef<User>[] => [
  {
    accessorKey: "employeeId",
    header: "Employee ID",
  },
  {
    accessorKey: "firstName",
    header: "Employee Name",
    cell: ({ row }) => `${row.original.firstName} ${row.original.lastName}`,
  },
  {
    accessorKey: "role",
    header: "Roles",
    cell: ({ row }) => (
      <Badge
        className={`${getRoleBadgeColor(
          row.original.role
        )} rounded px-3 py-2 border-0 `}
        variant="outline"
      >
        {row.original?.role?.toUpperCase()}
      </Badge>
    ),
  },
  {
    accessorKey: "email",
    header: "Email Address",
  },
  {
    accessorKey: "isActive",
    header: "Status",
    cell: ({ row }) => {
      const user = row.original
      return (
        <div className="flex items-center bg-grey-50 w-fit px-3 py-2 rounded">
          <Switch
            checked={user.isActive}
            onCheckedChange={() => onToggleUserStatus(user)}
            className={`mr-2 ${
              user.isActive ? "data-[state=checked]:bg-green-600" : ""
            }`}
          />
          <span className={`text-sm font-medium text-grey-900`}>
            {user.isActive ? "ACTIVE" : "INACTIVE"}
          </span>
        </div>
      )
    },
  },
  {
    id: "actions",
    header: "Actions",
    cell: ({ row }) => {
      const user = row.original

      return (
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            className="bg-gray-100 hover:bg-gray-200 border-gray-200"
            onClick={() => onEditUser(user)}
          >
            <Pencil  className="h-4 w-4 text-gray-600" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="bg-gray-100 hover:bg-gray-200 border-gray-200"
            onClick={() => resetUserPassword(user.id)}
          >
            <RotateCcwKey className="h-4 w-4 text-gray-600" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="bg-red-50 hover:bg-red-100 border-red-200"
            onClick={() => onDeleteUser(user)}
          >
            <Trash2 className="h-4 w-4 text-red-600" />
          </Button>
        </div>
      )
    },
  },
]
