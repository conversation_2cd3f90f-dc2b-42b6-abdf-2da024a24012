export interface WeeklySummary {
  weekNumber: number;
  startDate: string; // ISO Date string
  endDate: string;   // ISO Date string
  totalCost: number;
}

export interface Resource {
  id: string;
  name: string;
  resourceCode: string;
  unitType: string;
  unitSize: number;
  imageUrl: string;
  resourceCategory: string;
  minInventoryQty: number;
  maxInventoryQty: number;
  resourceType: string;
  deletedAt: string | null;
}

export interface ResourceCost {
  resource: Resource;
  cost: number;
}

export interface Project {
  id: string;
  projectCode: string;
  projectName: string;
  startDate: string;  // ISO Date string
  dueDate: string;    // ISO Date string
  createdAt: string;  // ISO Date string
  updatedAt: string;  // ISO Date string
}

export interface ProjectCost {
  project: Project;
  totalCost: number;
  resourcesCost: ResourceCost[];
}

export interface ResourceCategoryCost {
  categoryName: string;
  totalCost: number;
}

export interface CostReport {
  weeklySummaries: WeeklySummary[];
  projectCosts: ProjectCost[];
  resourceCategoryCosts: ResourceCategoryCost[];
}
