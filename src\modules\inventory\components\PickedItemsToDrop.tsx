import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, X } from "lucide-react";
import ItemCard from "./ItemCard";
import type { InventoryItem, Transaction } from "@/types/inventory";
import { useEffect, useMemo, useState } from "react";
import { getPickedItemsForDrop } from "@/services/api";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { CheckoutItem } from "./Pick";

interface PickedItemsToDropProps {
  onDrop: (item: Transaction) => void;
  onRemoveFromCheckout?: (item: Transaction) => void;
  refreshTrigger?: number;
  checkoutItems?: CheckoutItem[];
}

const PickedItemsToDrop = ({ onDrop, onRemoveFromCheckout, refreshTrigger, checkoutItems = [] }: PickedItemsToDropProps) => {
  const [pickedItems, setPickedItems] = useState<Transaction[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [itemType, setItemType] = useState("all");
  const [error, setError] = useState<string | null>(null);

  const fetchItems = async () => {
    try {
      const data: InventoryItem = await getPickedItemsForDrop();
      setPickedItems(data.transactions);
    } catch (error) {
      console.error("Failed to fetch items", error);
      setError("Failed to load items.");
    }
  };

  useEffect(() => {
    fetchItems();
  }, []);

  // Refetch items when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      fetchItems();
    }
  }, [refreshTrigger]);

  const filteredItems = useMemo(() => {
    let filtered = pickedItems;

    if (searchTerm) {
      filtered = filtered.filter((item) =>
        item.resourceName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (itemType !== "all") {
      filtered = filtered.filter(
        (item) => item.resourceCategory.toLowerCase() === itemType
      );
    }

    return filtered;
  }, [searchTerm, itemType, pickedItems]);

  return (
    <div className="flex-grow">
      {error && <div className="text-red-500 mb-4">{error}</div>}
      {/* <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">
          Picked Items{" "}
          <span className="text-green-500 text-sm">
            {filteredItems.length} items
          </span>
        </h2>
      </div> */}

      <div className="flex items-center gap-4">
        <h2 className="text-2xl font-bold">Picked Items</h2>
        <Badge
          variant="secondary"
          className="bg-success-50 text-success-500 border-success-200 rounded-full"
        >
          {filteredItems.length} Items
        </Badge>
      </div>
      <div className="flex items-center justify-between gap-4 mt-6 mb-5">
        <div className="relative w-64">
          <Input
            placeholder="Search Items..."
            className="pr-10"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          {searchTerm ? (
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-8 top-1/2 -translate-y-1/2 w-5 h-5"
              onClick={() => setSearchTerm("")}
            >
              <X className="w-4 h-4" />
            </Button>
          ) : (
            <Search className="absolute right-3 top-1/2 -translate-y-1/2 w-5 h-5 text-gray-400" />
          )}
        </div>
        <Select onValueChange={setItemType} value={itemType}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="All Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Type</SelectItem>
            <SelectItem value="consumable">Consumable</SelectItem>
            <SelectItem value="asset">Asset</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-4 overflow-y-auto h-[65vh]">
        {filteredItems.map((item) => {
          const isInCheckout = checkoutItems.some(checkoutItem => checkoutItem.id === item.resourceId);
          
          const handleRemove = () => {
            if (onRemoveFromCheckout) {
              onRemoveFromCheckout(item); // Pass the original Transaction item
            }
          };
          
          return (
            <ItemCard
              key={item.resourceId}
              item={item}
              variant="drop"
              onDrop={onDrop}
              onRemove={handleRemove}
              isInCheckout={isInCheckout}
            />
          );
        })}
      </div>
    </div>
  );
};

export default PickedItemsToDrop;
