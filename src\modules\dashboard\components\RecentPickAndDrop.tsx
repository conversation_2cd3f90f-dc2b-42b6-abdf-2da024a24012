import { useState, useEffect } from "react";
import { getRecentTransactions } from "../../../services/api";
import type { Transaction } from "../../../types/inventory";

const RecentPickAndDrop = () => {
  const [activeTab, setActiveTab] = useState<"pick" | "drop">("pick");
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getRecentTransactions(10, true);
        setTransactions(data.transactions || []);
      } catch (err) {
        console.error('Error fetching recent transactions:', err);
        setError('Failed to fetch recent activities');
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, []);

  // Filter transactions based on active tab
  const filteredTransactions = transactions.filter(
    transaction => transaction.actionType.toLowerCase() === activeTab
  );

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp);
    return {
      date: date.toLocaleDateString('en-GB'),
      time: date.toLocaleTimeString('en-US', { 
        hour12: true, 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    };
  };

  return (
    <div className="bg-white rounded-lg border p-6 mb-8">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Recent Pick and Drop</h3>
        <div className="flex rounded-lg border overflow-hidden">
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === "pick"
                ? "bg-blue-500 text-white"
                : "text-gray-600 hover:text-gray-900"
            }`}
            onClick={() => setActiveTab("pick")}
          >
            Pick
          </button>
          <button
            className={`px-4 py-2 text-sm font-medium ${
              activeTab === "drop"
                ? "bg-blue-500 text-white"
                : "text-gray-600 hover:text-gray-900"
            }`}
            onClick={() => setActiveTab("drop")}
          >
            Drop
          </button>
        </div>
      </div>

      <div className="space-y-4 max-h-80 overflow-y-auto">
        {loading ? (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="text-center py-8">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        ) : filteredTransactions.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-500 text-sm">No recent {activeTab} activities found</p>
          </div>
        ) : (
          filteredTransactions.map((transaction) => {
            const { date, time } = formatDate(transaction.timestamp);
            return (
              <div key={transaction.id} className="flex items-center gap-4 p-3 hover:bg-gray-50 rounded-lg">
                <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center overflow-hidden">
                  {transaction.resourceImageUrl ? (
                    <img 
                      src={transaction.resourceImageUrl} 
                      alt={transaction.resourceName}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-6 h-6 bg-orange-500 rounded"></div>
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{transaction.resourceName}</h4>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>{transaction.resourceId}</span>
                    <span>Count: {transaction.quantity}</span>
                    <span className="capitalize">{transaction.actionType}</span>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900">{transaction.userName}</p>
                  <p className="text-sm text-gray-600">{date}, {time}</p>
                </div>
              </div>
            );
          })
        )}
      </div>
    </div>
  );
};

export default RecentPickAndDrop;
