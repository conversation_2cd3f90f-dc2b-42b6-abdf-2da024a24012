import { useState } from "react";
import { ErrorState, LoadingState } from "@/components/common/PageStates";
import type { User, UserFormData } from "@/types/user";
import AddOrEditUserDrawer from "./components/AddOrEditUserDrawer";
import ResetPasswordModal from "./components/ResetPasswordModal";
import DeactivateUserModal from "./components/DeactivateUserModal";
import DeleteUserModal from "./components/DeleteUserModal";
import UserManagementHeader from "./components/UserManagementHeader";
import UserManagementFilters from "./components/UserManagementFilters";
import { DataTable } from "@/components/DataTable";
import { getColumns } from "./components/columns";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getUsers, createUser, updateUser } from "@/services/api";

const userKeys = {
  all: ["users"] as const,
  lists: () => [...userKeys.all, "list"] as const,
};

const UserManagementPage = () => {
  const queryClient = useQueryClient();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [isResetPasswordModalOpen, setIsResetPasswordModalOpen] = useState(false);
  const [userToResetPassword, setUserToResetPassword] = useState<User | null>(null);
  const [isDeactivateModalOpen, setIsDeactivateModalOpen] = useState(false);
  const [userToToggle, setUserToToggle] = useState<User | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);

  const {
    data: users = [],
    isLoading,
    error,
  } = useQuery<User[]>({
    queryKey: userKeys.lists(),
    queryFn: getUsers,
  });

  const { mutateAsync: addUser, isPending: isCreating } = useMutation({
    mutationFn: createUser,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
    },
  });

  const { mutateAsync: updateUserData } = useMutation({
    mutationFn: ({
      userId,
      userData,
    }: {
      userId: string;
      userData: Partial<User>;
    }) => updateUser(userId, userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
    },
  });

  const resetUserPassword = async (userId: string) => {
    // Find the user to reset password for
    const user = users.find(u => u.id === userId);
    if (user) {
      setUserToResetPassword(user);
      setIsResetPasswordModalOpen(true);
    }
  };

  const handleConfirmResetPassword = async () => {
    if (userToResetPassword) {
      try {
        // In a real application, this would make an API call to reset the password
        console.log(`Password reset confirmed for user ${userToResetPassword.id}`);
        // You could show a toast notification here
        // await resetPassword(userToResetPassword.id);
        setIsResetPasswordModalOpen(false);
        setUserToResetPassword(null);
      } catch (error) {
        console.error("Failed to reset password:", error);
      }
    }
  };

  const handleCloseResetPasswordModal = () => {
    setIsResetPasswordModalOpen(false);
    setUserToResetPassword(null);
  };

  const handleToggleUserStatus = (user: User) => {
    setUserToToggle(user);
    setIsDeactivateModalOpen(true);
  };

  const handleConfirmToggleStatus = async () => {
    // The modal will handle the API call internally
    // This function is called after successful API call to refresh data and close modal
    if (userToToggle) {
      // Refresh the users data to reflect the change
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      setIsDeactivateModalOpen(false);
      setUserToToggle(null);
    }
  };

  const handleCloseDeactivateModal = () => {
    setIsDeactivateModalOpen(false);
    setUserToToggle(null);
  };

  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setIsAddModalOpen(true); // Reuse the same modal state
  };

  const handleDeleteUser = (user: User) => {
    setUserToDelete(user);
    setIsDeleteModalOpen(true);
  };

  const handleConfirmDeleteUser = async () => {
    // The modal will handle the API call internally
    // This function is called after successful API call to refresh data and close modal
    if (userToDelete) {
      // Refresh the users data to reflect the change
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      setIsDeleteModalOpen(false);
      setUserToDelete(null);
    }
  };

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setUserToDelete(null);
  };

  const columns = getColumns({
    resetUserPassword,
    onEditUser: handleEditUser,
    onDeleteUser: handleDeleteUser,
    onToggleUserStatus: handleToggleUserStatus,
  });


  const handleAddUser = async (userData: UserFormData) => {
    try {
      if (editingUser) {
        // Edit mode - update existing user
        await updateUserData({ userId: editingUser.id, userData });
      } else {
        // Add mode - create new user
        await addUser(userData);
      }
      setIsAddModalOpen(false);
      setEditingUser(null);
    } catch (error) {
      console.error("Failed to save user:", error);
      // You could show a toast notification here
    }
  };


  // Loading state
  if (isLoading) {
    return <LoadingState message="Loading users..." />;
  }

  // Error state
  if (error) {
    return <ErrorState error={error} />;
  }

  return (
    <div className="p-6 bg-white min-h-[90vh]">
      <UserManagementHeader
        setIsAddModalOpen={setIsAddModalOpen}
        isCreating={isCreating}
      />

      <UserManagementFilters users={users} onFilter={setFilteredUsers} />

      <DataTable columns={columns} data={filteredUsers} />

      {/* Add/Edit User Drawer */}
      <AddOrEditUserDrawer
        isOpen={isAddModalOpen}
        onClose={() => {
          setIsAddModalOpen(false);
          setEditingUser(null);
        }}
        onSubmit={handleAddUser}
        user={editingUser}
      />

      {/* Reset Password Modal */}
      <ResetPasswordModal
        isOpen={isResetPasswordModalOpen}
        onClose={handleCloseResetPasswordModal}
        onConfirm={handleConfirmResetPassword}
        user={userToResetPassword}
      />

      {/* Deactivate User Modal */}
      <DeactivateUserModal
        isOpen={isDeactivateModalOpen}
        onClose={handleCloseDeactivateModal}
        onConfirm={handleConfirmToggleStatus}
        user={userToToggle}
        isDeactivating={userToToggle?.isActive ?? false}
      />

      {/* Delete User Modal */}
      <DeleteUserModal
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDeleteUser}
        user={userToDelete}
      />
    </div>
  );
};

export default UserManagementPage;
