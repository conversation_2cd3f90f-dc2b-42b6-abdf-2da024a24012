import { Link, useParams } from "react-router-dom";
import { useSupplier } from "../hooks/use-supplier";
import { useResourcesBySupplier } from "@/modules/resources/hooks/use-resources-by-supplier";
import { Button } from "@/components/ui/button";
import { ChevronLeft, Store, Phone, User, Mail } from "lucide-react";
import {
  EmptyState,
  ErrorState,
  LoadingState,
} from "@/components/common/PageStates";
import SupplierResourceTable from "../components/supplierResourceTable/SupplierResourceTable";

const SupplierDetailsPage = () => {
  const { id } = useParams<{ id: string }>();
  const {
    data: supplier,
    isLoading: isSupplierLoading,
    isError: isSupplierError,
    error: supplierError,
  } = useSupplier(id ?? "");

  const {
    data: resources,
    isLoading: isResourcesLoading,
    error: resourcesError,
  } = useResourcesBySupplier(id ?? "");

  if (isSupplierLoading) {
    return <LoadingState message="Loading supplier details..." />;
  }

  if (isSupplierError) {
    return <ErrorState error={supplierError as Error} />;
  }

  if (!supplier) {
    return <EmptyState text="No supplier found." />;
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="flex items-center mb-6">
        <Button variant="ghost" asChild>
          <Link to="/suppliers">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back
          </Link>
        </Button>
      </div>

      <div className="bg-white p-6 rounded-lg shadow-sm mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 items-center">
          <div className="flex items-center gap-4">
            <div className="bg-blue-100 p-3 rounded-lg">
              <Store className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-800">
                {supplier.name}
              </h2>
              <p className="text-sm text-gray-500">{supplier.code}</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Phone className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-500">Contact number</p>
              <p className="font-medium text-gray-800">
                {supplier.phoneNumber}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <User className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-500">Contact person</p>
              <p className="font-medium text-gray-800">
                {supplier.contactPerson}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Mail className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-500">Contact email</p>
              <p className="font-medium text-gray-800">{supplier.contactEmail}</p>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-2xl font-semibold text-gray-800 mb-4">Resources</h3>
        <SupplierResourceTable
          data={resources?.resources ?? []}
          isLoading={isResourcesLoading}
          error={resourcesError as Error | null}
        />
      </div>
      {/* <div>
        <h3 className="text-2xl font-semibold text-gray-800 mb-4">Resources</h3>
        <ResourceTable
          data={resources ?? []}
          isLoading={isResourcesLoading}
          error={resourcesError as Error | null}
          onResourceUpdate={refetchResources}
        />
      </div> */}
    </div>
  );
};

export default SupplierDetailsPage;
