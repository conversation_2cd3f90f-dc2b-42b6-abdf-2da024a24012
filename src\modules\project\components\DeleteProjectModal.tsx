import React from "react";
import ConfirmationModal from "@/components/common/ConfirmationModal";
import type { Project } from "@/types/project";

interface DeleteProjectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  project: Project | null;
  onConfirm: (project: Project) => void;
}

const DeleteProjectModal: React.FC<DeleteProjectModalProps> = ({
  open,
  onOpenChange,
  project,
  onConfirm,
}) => {
  const handleConfirm = () => {
    if (project) {
      onConfirm(project);
    }
    onOpenChange(false);
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <ConfirmationModal
      open={open}
      onOpenChange={onOpenChange}
      title="Delete project"
      description="Are you sure you want to delete this project?"
      confirmText="Delete"
      cancelText="Cancel"
      onConfirm={handleConfirm}
      onCancel={handleCancel}
      item={
        project
          ? {
              name: project.projectName,
              code: project.projectCode,
            }
          : undefined
      }
    />
  );
};

export default DeleteProjectModal;
