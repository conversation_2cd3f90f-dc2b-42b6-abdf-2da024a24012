import { ChevronRight } from "lucide-react";
import type { ResourceCategoryCost, ProjectCost } from "../../../types/reports";

interface ProductCategoriesProps {
  resourceCategoryCosts: ResourceCategoryCost[];
  projectCosts: ProjectCost[];
}

const ProductCategories = ({ resourceCategoryCosts, projectCosts }: ProductCategoriesProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Product Categories Cost */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Product Categories Cost</h3>
          <button className="flex items-center gap-1 text-blue-500 text-sm font-medium hover:text-blue-600">
            View All
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
        
        <div className="space-y-3">
          {resourceCategoryCosts.length > 0 ? (
            resourceCategoryCosts.map((category, index) => (
              <div key={index} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-lg">📦</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{category.categoryName}</h4>
                  <p className="text-sm text-gray-600">Category items</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900">${category.totalCost.toFixed(2)}</p>
                </div>
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center py-4">No category data available</p>
          )}
        </div>
      </div>

      {/* Project Breakdown by Cost */}
      <div className="bg-white rounded-lg border p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Project Breakdown by cost</h3>
          <button className="flex items-center gap-1 text-blue-500 text-sm font-medium hover:text-blue-600">
            View All
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
        
        <div className="space-y-3">
          {projectCosts.length > 0 ? (
            projectCosts.slice(0, 4).map((projectCost, index) => (
              <div key={index} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-lg">📊</span>
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{projectCost.project.projectName}</h4>
                  <p className="text-sm text-gray-600">{projectCost.project.projectCode}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium text-gray-900">${projectCost.totalCost.toFixed(2)}</p>
                </div>
              </div>
            ))
          ) : (
            <p className="text-gray-500 text-center py-4">No project data available</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCategories;
