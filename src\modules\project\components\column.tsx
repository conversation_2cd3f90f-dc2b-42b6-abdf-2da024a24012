import { Button } from "@/components/ui/button";
import type { Row, Table, ColumnDef } from "@tanstack/react-table";
import { Trash2 } from "lucide-react";
import type { Project } from "@/types/project";

export const projectTableColumns: ColumnDef<Project>[] = [
	{
		accessorKey: "projectName",
		header: "PROJECT NAME",
	},
	{
		accessorKey: "projectCode",
		header: "PROJECT CODE",
	},
	{
		accessorKey: "startDate",
		header: "START DATE",
	},
	{
		accessorKey: "dueDate",
		header: "DUE DATE",
	},
	{
		accessorKey: "actions",
		header: "ACTIONS",
		cell: ({ row, table }: { row: Row<Project>; table: Table<Project> }) => (
			<Button
				variant="destructive"
				size="icon"
				onClick={() => (table.options.meta as any)?.onDelete?.(row.original)}
			>
				<Trash2 className="h-4 w-4" />
			</Button>
		),
	},
];
