import axios from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
import type { InventoryData, ResourceFormData } from 'src/types/resource';
import type { UserFormData } from 'src/types/user';
import type { SupplierFormData } from 'src/types/supplier';
import type { ProjectFormData } from 'src/types/project';
import type { CostReport } from 'src/types/reports';

// Define ProjectData interface for reports
export interface ProjectData {
  projectCode: string;
  projectName: string;
  startDate: string;
  endDate: string;
  resources: number;
  totalQuantity: number;
  totalCost: string;
  status?: 'ACTIVE' | 'PAST' | 'UPCOMING';
}

// Define API response interface for project consumable costs
export interface ProjectConsumableCostResponse {
  project: {
    id: string;
    projectCode: string;
    projectName: string;
    startDate: string;
    dueDate: string;
    createdAt: string;
    updatedAt: string;
  };
  totalCost: number;
  resourcesCost: Array<{
    resource: {
      id: string;
      name: string;
      resourceCode: string;
      unitType: string;
      unitSize: number;
      imageUrl: string;
      resourceCategory: string;
      minInventoryQty: number;
      maxInventoryQty: number;
      resourceType: string;
      deletedAt: string | null;
    };
    cost: number;
  }>;
}

// Base API configuration
const BASE_URL = '/api'; // Using proxy path
// const BASE_URL = 'http://localhost:4000/api'; 

// Create axios instance with base configuration
export const apiClient: AxiosInstance = axios.create({
  baseURL: BASE_URL,
  timeout: 10000, // 10 seconds timeout
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding bearer token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling errors
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  (error) => {
    // Handle common errors (401, 403, etc.)
    if (error.response && error.response.status === 401) {
      // Clear authentication data
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('permissions');
      localStorage.removeItem('temp_token');
      localStorage.removeItem('userId');
      
      // Redirect to login page
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Resource endpoints
export const getResources = async () => {
  const response = await apiClient.get('/resources/inventory');
  return response.data;
};

export const getProjects = async () => {
  const response = await apiClient.get('/projects');
  return response.data;
};

export const createProject = async (projectData: ProjectFormData) => {
  const response = await apiClient.post('/projects', projectData);
  return response.data;
};

export const deleteProject = async (projectId: string) => {
  const response = await apiClient.delete(`/projects/${projectId}`);
  return response.data;
};

export const createResource = async (resourceData: ResourceFormData) => {
  const response = await apiClient.post('/resources', resourceData);
  return response.data;
};

export const updateResource = async (resourceId: string, resourceData: ResourceFormData) => {
  const response = await apiClient.put(`/resources/${resourceId}`, resourceData);
  return response.data;
};

export const addStock = async (resourceId: string, stockData: InventoryData) => {
  const response = await apiClient.post(`/resources/${resourceId}/inventory`, stockData);
  return response.data;
};

export const getResourceInventory = async (resourceId: string) => {
  const response = await apiClient.get(`/resources/${resourceId}/inventory`);
  return response.data;
};

// User endpoints
export const getUsers = async () => {
  const response = await apiClient.get('/users');
  return response.data;
};

export const createUser = async (userData: UserFormData) => {
  const response = await apiClient.post('/users', userData);
  return response.data;
};

export const updateUser = async (userId: string, userData: Partial<UserFormData>) => {
  const response = await apiClient.put(`/users/${userId}`, userData);
  return response.data;
};

export const deleteUser = async (userId: string) => {
  const response = await apiClient.delete(`/users/${userId}`);
  return response.data;
};

export const resetUserPassword = async (userId: string) => {
  const response = await apiClient.post('/auth/resetpassword', { userId });
  return response.data;
};

export const changePassword = async (passwordData: {
  userId: string;
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}) => {
  // Create a separate axios instance without the redirect interceptor for password changes
  const passwordClient = axios.create({
    baseURL: BASE_URL,
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Add token to request
  const token = localStorage.getItem('temp_token');
  if (token) {
    passwordClient.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  }

  const response = await passwordClient.post('/auth/change-password', passwordData);
  return response.data;
};

// File upload endpoint
export const uploadFile = async (file: File) => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await apiClient.post("/file-upload", formData, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return response.data;
};

// Get all locations
export const getLocations = async (): Promise<string[]> => {
  // Make the API request without token for now
  const response = await apiClient.get('/resources/locations');
  return response.data;
};

// Supplier endpoints
export const getSuppliers = async () => {
  const response = await apiClient.get('/suppliers');
  return response.data;
};

export const createSupplier = async (supplierData: SupplierFormData) => {
  const response = await apiClient.post('/suppliers', supplierData);
  return response.data;
};

export const updateSupplier = async (supplierId: string, supplierData: SupplierFormData) => {
  const response = await apiClient.put(`/suppliers/${supplierId}`, supplierData);
  return response.data;
};

export const getSupplier = async (id: string) => {
  const response = await apiClient.get(`/suppliers/${id}`);
  return response.data;
};

export const deleteSupplier = async (supplierId: string) => {
  const response = await apiClient.delete(`/suppliers/${supplierId}`);
  return response.data;
};

export const getResourcesBySupplier = async (supplierId: string) => {
  const response = await apiClient.get(`/suppliers/${supplierId}/resources`);
  return response.data;
}

export const getInventoryItems = async () => {
  const response = await apiClient.get('/resources/inventory');
  return response.data;
};

export const getPickedItemsForDrop = async () => {
  const response = await apiClient.get('/resources/inventory/transactions?actionType=pick');
  return response.data;
};

export const pickItems = async (pickData: {
  items: Array<{
    resourceId: string;
    quantity: number;
    unitType: string;
  }>;
  userId: string;
  projectId: string;
}) => {
  const response = await apiClient.post('/resources/inventory/pick', pickData);
  return response.data;
};

export const dropItems = async (dropData: {
  items: Array<{
    resourceId: string;
    quantity: number;
    unitType: string;
  }>;
  userId: string;
  projectId: string;
}) => {
  const response = await apiClient.post('/resources/inventory/drop', dropData);
  return response.data;
};

export const getRecentTransactions = async (limit: number = 10, includeZeroQuantity: boolean = true) => {
  const params = new URLSearchParams({
    limit: limit.toString(),
    includeZeroQuantity: includeZeroQuantity.toString()
  });
  const response = await apiClient.get(`/resources/inventory/transactions?${params.toString()}`);
  return response.data;
};

// Reports endpoints
export const getReportsSummary = async (startDate?: string, endDate?: string): Promise<CostReport> => {
  let url = '/reports/summary';
  
  if (startDate && endDate) {
    const params = new URLSearchParams({
      startDate,
      endDate
    });
    url += `?${params.toString()}`;
  }
  
  const response = await apiClient.get(url);
  return response.data;
};

export const getProjectConsumableCosts = async (startDate?: string, endDate?: string): Promise<ProjectConsumableCostResponse[]> => {
  let url = '/reports/project-consumable-costs';
  
  if (startDate && endDate) {
    const params = new URLSearchParams({
      startDate,
      endDate
    });
    url += `?${params.toString()}`;
  }
  
  const response = await apiClient.get(url);
  return response.data;
};
