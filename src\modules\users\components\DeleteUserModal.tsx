import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { AlertTriangle } from 'lucide-react'
import type { User } from '@/types/user'
import { deleteUser } from '@/services/api'

interface DeleteUserModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => void
  user: User | null
}

const DeleteUserModal: React.FC<DeleteUserModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  user
}) => {
  const [isLoading, setIsLoading] = useState(false)

  if (!user) return null

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "admin":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "supervisor":
        return "bg-red-100 text-red-800 border-red-200";
      case "mechanic":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getUserInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase()
  }

  const handleConfirmDelete = async () => {
    setIsLoading(true)
    try {
      // Delete user via API
      await deleteUser(user.id)
      
      // Call parent's onConfirm to refresh data and close modal
      onConfirm()
    } catch (error) {
      console.error('Failed to delete user:', error)
      // You could show an error toast here
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (!isLoading) {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="text-center">
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
          <DialogTitle className="text-xl font-semibold text-gray-900">
            Delete user
          </DialogTitle>
        </DialogHeader>
        
        <div className="text-center">
          <p className="text-sm text-gray-600 mb-6">
            Are you sure you want to delete this user? This action cannot be undone.
          </p>
          
          {/* User Info Card */}
          <div className="flex items-center justify-between bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-3">
              <Avatar className="h-10 w-10">
                <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                  {getUserInitials(user.firstName, user.lastName)}
                </AvatarFallback>
              </Avatar>
              <div className="text-left">
                <p className="font-medium text-gray-900">
                  {user.firstName} {user.lastName}
                </p>
              </div>
            </div>
            <Badge
              className={`${getRoleBadgeColor(user.role)} rounded px-3 py-1 text-xs font-medium border-0`}
              variant="outline"
            >
              {user?.role?.toUpperCase()}
            </Badge>
          </div>
          
          {/* Action Buttons */}
          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={handleClose}
              className="flex-1 border-gray-300 text-gray-700 hover:bg-gray-50"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmDelete}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white"
              disabled={isLoading}
            >
              {isLoading ? 'Deleting...' : 'Delete'}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default DeleteUserModal
