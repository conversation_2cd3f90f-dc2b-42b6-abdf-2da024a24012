"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { SupplierResource } from "@/types/supplier";

export const columns: ColumnDef<SupplierResource>[] = [
  {
    accessorKey: "image",
    header: "IMAGE",
    cell: ({ row }) => {
      const image = row.getValue("image") as string;
      return (
        <div className="flex items-center justify-center">
          {image ? (
            <img
              src={image}
              alt={row.getValue("name") as string}
              className="h-12 w-12 rounded-md object-cover"
            />
          ) : (
            <div className="h-12 w-12 rounded-md bg-gray-100 flex items-center justify-center">
              <span className="text-gray-400 text-xs">No Image</span>
            </div>
          )}
        </div>
      );
    },
  },
  {
    accessorKey: "name",
    header: "ITEM NAME",
  },
  {
    accessorKey: "code",
    header: "ITEM CODE",
  },
  {
    accessorKey: "category",
    header: "CATEGORY",
  },
  {
    accessorKey: "unit",
    header: "UNIT",
  },
];
