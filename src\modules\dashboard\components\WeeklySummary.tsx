import { ChevronDown } from "lucide-react";
import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, Y<PERSON><PERSON>s, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from 'recharts';
import type { WeeklySummary as WeeklySummaryType } from "../../../types/reports";

interface WeeklySummaryProps {
  weeklyData: WeeklySummaryType[];
}

const WeeklySummary = ({ weeklyData }: WeeklySummaryProps) => {
  const [selectedPeriod] = useState("Current Month");

  // Transform API data to chart format
  const chartData = weeklyData.map((week) => ({
    week: `Week ${week.weekNumber}`,
    cost: week.totalCost,
    startDate: week.startDate,
    endDate: week.endDate
  }));

  // Use only real data, no fallback
  const hasData = chartData.length > 0;

  // Custom tooltip component
  const CustomTooltip = ({ active, payload, label }: {
    active?: boolean;
    payload?: Array<{ value: number }>;
    label?: string;
  }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white border rounded-lg shadow-lg p-3">
          <p className="text-sm font-medium text-gray-900">{`${label}`}</p>
          <p className="text-sm text-blue-600">
            {`Total Cost: $${payload[0].value}`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="bg-white rounded-lg border p-6 mb-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-1">Weekly Summary</h3>
          <p className="text-sm text-gray-600">Review your cost summary, week by week.</p>
        </div>
        <div className="relative">
          <button className="flex items-center gap-2 px-4 py-2 border rounded-lg text-sm">
            {selectedPeriod}
            <ChevronDown className="w-4 h-4" />
          </button>
        </div>
      </div>

      <div className="space-y-4">
        <div className="h-64">
          {hasData ? (
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart
                data={chartData}
                margin={{
                  top: 20,
                  right: 30,
                  left: 20,
                  bottom: 20,
                }}
              >
                <defs>
                  <linearGradient id="colorCost" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                    <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="week" 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                />
                <YAxis 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: '#666' }}
                  domain={[0, 600]}
                  tickFormatter={(value) => `$${value}`}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area 
                  type="linear" 
                  dataKey="cost" 
                  stroke="#3b82f6" 
                  strokeWidth={3}
                  fill="url(#colorCost)"
                  dot={{ fill: '#3b82f6', strokeWidth: 2, r: 6 }}
                  activeDot={{ r: 8, fill: '#3b82f6' }}
                />
              </AreaChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <p className="text-sm">No weekly summary data available</p>
                <p className="text-xs text-gray-400 mt-1">Data will appear when transactions are recorded</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WeeklySummary;
