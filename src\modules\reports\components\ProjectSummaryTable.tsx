import { Eye, Download } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { DataTable } from '@/components/DataTable'
import { type ColumnDef } from '@tanstack/react-table'
import { useNavigate } from 'react-router-dom'

export interface ProjectData {
  projectCode: string
  projectName: string
  startDate: string
  endDate: string
  resources: number
  totalQuantity: number
  totalCost: string
  status: 'ACTIVE' | 'PAST' | 'UPCOMING'
  id?: string // Add optional id field for navigation
}

interface ProjectSummaryTableProps {
  data: ProjectData[]
}

const ProjectSummaryTable = ({ data }: ProjectSummaryTableProps) => {
  const navigate = useNavigate()

  const handleViewProject = (project: ProjectData) => {
    // Use project id if available, otherwise use project code as fallback
    const projectId = project.id || project.projectCode
    navigate(`/projects/${projectId}`)
  }

  const columns: ColumnDef<ProjectData>[] = [
    {
      accessorKey: 'projectCode',
      header: 'PROJECT CODE',
    },
    {
      accessorKey: 'projectName',
      header: 'PROJECT NAME',
    },
    {
      accessorKey: 'startDate',
      header: 'START DATE',
    },
    {
      accessorKey: 'endDate',
      header: 'END DATE',
    },
    {
      accessorKey: 'resources',
      header: 'RESOURCES',
    },
    {
      accessorKey: 'totalQuantity',
      header: 'TOTAL QUANTITY',
    },
    {
      accessorKey: 'totalCost',
      header: 'TOTAL COST',
      cell: ({ row }: { row: { original: ProjectData } }) => (
        <span className="text-blue-600 font-medium">
          {row.original.totalCost}
        </span>
      ),
    },
    {
      id: 'actions',
      header: 'ACTIONS',
      cell: ({ row }: { row: { original: ProjectData } }) => (
        <div className="flex gap-2">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => handleViewProject(row.original)}
            title="View project details"
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="sm" title="Download report">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ]

  return (
    <div className="bg-white rounded-lg border">
      <DataTable
        columns={columns}
        data={data}
      />
    </div>
  )
}

export default ProjectSummaryTable
