"use client"

import type { ColumnDef } from "@tanstack/react-table"
import type { Supplier } from "@/types/supplier"
import { Link } from "react-router-dom"
import { But<PERSON> } from "@/components/ui/button"
import { Edit, Trash2 } from "lucide-react"

export const columns: ColumnDef<Supplier>[] = [
  {
    accessorKey: "code",
    header: "SUPPLIER CODE",
  },
  {
    accessorKey: "name",
    header: "SUPPLIER NAME",
  },
  {
    accessorKey: "phoneNumber",
    header: "PHONE NUMBER",
  },
  {
    accessorKey: "contactPerson",
    header: "CONTACT PERSON",
  },
  {
    id: "linkedResources",
    header: "LINKED RESOURCES",
    cell: ({ row }) => (
      <Link to={`/suppliers/${row.original.id}`} className="text-blue-500 hover:underline">
        View Resources
      </Link>
    ),
  },
  {
    id: "actions",
    header: "ACTIONS",
    cell: ({ row, table }) => (
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="icon"
          className="bg-[#F4F4F4]"
          onClick={() => (table.options.meta as any).onEdit(row.original)}
        >
          <Edit className="h-4 w-4" />
        </Button>
        <Button
          variant="destructive"
          size="icon"
          onClick={() => (table.options.meta as any).onDelete(row.original)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    ),
  },
]
