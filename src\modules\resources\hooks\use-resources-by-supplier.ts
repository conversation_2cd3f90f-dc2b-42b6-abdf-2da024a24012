import { useQuery } from "@tanstack/react-query";
import type { SupplierResourcesResponse } from "@/types/supplier";
import { getResourcesBySupplier } from "@/services/api";

export const useResourcesBySupplier = (supplierId: string) => {
  return useQuery<SupplierResourcesResponse, Error>({
    queryKey: ["resources", "supplier", supplierId],
    queryFn: () => getResourcesBySupplier(supplierId),
    enabled: !!supplierId,
  });
};
